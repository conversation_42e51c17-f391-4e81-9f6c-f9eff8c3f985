#!/usr/bin/env python3
"""
Wrapper script for launching the STEP viewer from the root directory.
This script forwards all arguments to the actual gui_step.py in the Tolery directory.
"""
import sys
import os
import subprocess

def main():
    # Get the directory where this script is located (should be the root directory)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the actual gui_step.py in the Tolery directory
    actual_gui_script = os.path.join(script_dir, "Tolery", "gui_step.py")
    
    if not os.path.exists(actual_gui_script):
        print(f"Error: STEP viewer script not found at {actual_gui_script}")
        sys.exit(1)
    
    # Forward all command line arguments to the actual script
    cmd = [sys.executable, actual_gui_script] + sys.argv[1:]
    
    try:
        # Launch the actual STEP viewer
        subprocess.run(cmd)
    except Exception as e:
        print(f"Error launching STEP viewer: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
