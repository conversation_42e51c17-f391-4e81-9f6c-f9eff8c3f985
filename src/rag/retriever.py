import os
import sys
from typing import List, Dict, Any, Optional
from langchain_core.documents import Document # Import Document

# Conditional imports based on execution context
if __name__ == '__main__' and (__package__ is None or __package__ == ''):
    _project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    if _project_root not in sys.path:
        sys.path.insert(0, _project_root)
    from src.rag.vector_store import load_index, search_index, METADATA_STORE as faiss_metadata
    from src.rag.structured_data_loader import load_csv_to_structured_data
    from src.rag.embedding import get_embedding_model
else:
    from .vector_store import load_index, search_index, METADATA_STORE as faiss_metadata
    from .structured_data_loader import load_csv_to_structured_data
    from .embedding import get_embedding_model

# --- Configuration ---
FAISS_INDEX_NAME = "main_rag_index.index" # Default name for the combined index
# Use absolute paths based on project root
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
FAISS_INDEX_PATH = os.path.join(PROJECT_ROOT, "data", "index", FAISS_INDEX_NAME)
METADATA_JSON_PATH = os.path.join(PROJECT_ROOT, "data", "index", "metadata_store.json")

# Define paths to CSV files
PERFORATED_SHEET_CSV_PATH = os.path.join(PROJECT_ROOT, "data", "Class", "Perforated sheet", "dictionary.csv")
TOLE_CSV_PATH = os.path.join(PROJECT_ROOT, "data", "Class", "Tole", "dictionary.csv")

# --- Global Variables to hold loaded data ---
faiss_index_instance = None
structured_perforated_sheet_data: Optional[Dict[str, Dict[str, Any]]] = None
structured_tole_data: Optional[Dict[str, Dict[str, Any]]] = None

def initialize_retriever(force_reload: bool = False):
    """
    Initializes all components of the retriever:
    - Loads the FAISS index and its metadata.
    - Loads structured data from CSV files.
    - Ensures the embedding model is ready.
    """
    global faiss_index_instance, structured_perforated_sheet_data, structured_tole_data

    print("Initializing RAG retriever...")

    # Ensure all required directories exist
    index_dir = os.path.dirname(FAISS_INDEX_PATH)
    perforated_sheet_dir = os.path.dirname(PERFORATED_SHEET_CSV_PATH)
    tole_dir = os.path.dirname(TOLE_CSV_PATH)

    os.makedirs(index_dir, exist_ok=True)
    os.makedirs(perforated_sheet_dir, exist_ok=True)
    os.makedirs(tole_dir, exist_ok=True)

    # Ensure embedding model is loaded (important for FAISS search)
    try:
        get_embedding_model()
    except Exception as e:
        print(f"Failed to initialize embedding model: {e}")
        # Depending on desired strictness, could raise an error or allow proceeding without embeddings

    if faiss_index_instance is None or force_reload:
        print(f"Loading FAISS index from {FAISS_INDEX_PATH}...")
        faiss_index_instance = load_index(index_name=FAISS_INDEX_NAME)
        if faiss_index_instance:
            print(f"FAISS index '{FAISS_INDEX_NAME}' loaded with {faiss_index_instance.ntotal} vectors.")
            print(f"Associated metadata store loaded with {len(faiss_metadata)} entries.")
        else:
            print(f"Failed to load FAISS index '{FAISS_INDEX_NAME}'. Semantic search will be unavailable.")
            # Note: The main_rag_index.index might not exist yet if build_rag_components.py hasn't run.

    if structured_perforated_sheet_data is None or force_reload:
        print(f"Loading structured data from {PERFORATED_SHEET_CSV_PATH}...")
        if os.path.exists(PERFORATED_SHEET_CSV_PATH):
            structured_perforated_sheet_data = load_csv_to_structured_data(PERFORATED_SHEET_CSV_PATH)
            if structured_perforated_sheet_data:
                print(f"'Perforated sheet' CSV data loaded with {len(structured_perforated_sheet_data)} entries.")
            else:
                print(f"Failed to load or empty data from '{PERFORATED_SHEET_CSV_PATH}'.")
        else:
            print(f"CSV file not found at {PERFORATED_SHEET_CSV_PATH}")
            structured_perforated_sheet_data = {}

    if structured_tole_data is None or force_reload:
        print(f"Loading structured data from {TOLE_CSV_PATH}...")
        if os.path.exists(TOLE_CSV_PATH):
            structured_tole_data = load_csv_to_structured_data(TOLE_CSV_PATH)
            if structured_tole_data:
                print(f"'Tole' CSV data loaded with {len(structured_tole_data)} entries.")
            else:
                print(f"Failed to load or empty data from '{TOLE_CSV_PATH}'.")
        else:
            print(f"CSV file not found at {TOLE_CSV_PATH}")
            structured_tole_data = {}

    print("RAG retriever initialization complete.")

def simple_keyword_param_extraction(query: str, class_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    A very simple keyword-based parameter extractor for structured data.
    Looks for keys from the class_data dictionary within the query.
    This is a placeholder and would need to be more robust in a real system.
    """
    extracted_params = {}
    if not class_data:
        return extracted_params

    # Iterate through all known parameter names (keys in the first item's 'Parameters' or 'Description' if available)
    # This is a naive approach; a better way would be to have a predefined list of queryable parameters.

    # For simplicity, let's assume we are looking for specific terms mentioned in the CSV 'Name' or 'Description'
    for name_key, data_item in class_data.items():
        if name_key.lower() in query.lower(): # If the "Name" from CSV is in query
            extracted_params[name_key] = data_item # Return the whole data item for that name
            # Could also try to parse 'Parameters' or 'Description' for more fine-grained matching
            # e.g. if data_item['Parameters'] = "length,width" and query mentions "length", extract that.
            # This is highly dependent on the CSV structure and query patterns.

        description = data_item.get("Description", "")
        if description and isinstance(description, str) and description.lower() in query.lower():
             extracted_params[name_key] = data_item # If description matches

    # This is very basic. A more advanced version would parse the query for specific parameter values.
    # For example, if query is "Perforated sheet R12 U27.72 with length 500", it should identify "R12 U27.72"
    # and potentially "length: 500".
    return extracted_params


def retrieve_context(query: str, k_semantic: int = 10) -> List[Document]:
    """
    Retrieves context for a given query and returns it as a list of LangChain Documents.
    Combines semantic search results with structured data lookups.
    """
    if faiss_index_instance is None and structured_perforated_sheet_data is None and structured_tole_data is None:
        initialize_retriever() # Ensure components are loaded

    retrieved_documents: List[Document] = []

    # 1. Semantic Search
    if faiss_index_instance:
        semantic_hits = search_index(faiss_index_instance, query, k=k_semantic)
        for hit in semantic_hits:
            page_content = hit.get("text_content", "")
            # Ensure all metadata values are serializable (e.g. convert numpy floats to python floats if any)
            metadata = {k: v for k, v in hit.items() if k != "text_content"}
            if 'similarity_score' in metadata and isinstance(metadata['similarity_score'], (float, int)):
                 metadata['similarity_score'] = float(metadata['similarity_score']) # Ensure it's a standard float
            else: # if score is missing or not a number, provide a default or remove
                metadata.pop('similarity_score', None)

            retrieved_documents.append(Document(page_content=page_content, metadata=metadata))
    else:
        print("FAISS index not available for semantic search.")

    # 2. Structured Data Access
    query_lower = query.lower()

    def process_structured_class(class_name_query: str, class_name_label: str, class_data: Optional[Dict[str, Dict[str, Any]]]):
        if class_data and class_name_query in query_lower:
            for name_key, data_item in class_data.items():
                if name_key.lower() in query_lower:
                    # Format structured data into readable text for page_content
                    content_parts = [f"Structured data for {class_name_label} '{name_key}':"]
                    if data_item.get("Parameters"):
                        content_parts.append(f"  Parameters: {data_item['Parameters']}")
                    if data_item.get("Description"):
                        content_parts.append(f"  Description: {data_item['Description']}")

                    page_content = "\n".join(content_parts)
                    metadata = {
                        "source": "structured_data_dictionary",
                        "class": class_name_label,
                        "model_code": name_key,
                        **data_item # Add all original CSV fields to metadata
                    }
                    retrieved_documents.append(Document(page_content=page_content, metadata=metadata))

    process_structured_class("perforated sheet", "Perforated sheet", structured_perforated_sheet_data)
    process_structured_class("tole", "Tole", structured_tole_data)

    return retrieved_documents

if __name__ == "__main__":
    print("Running RAG Retriever Test...")
    initialize_retriever(force_reload=True) # Force reload for testing

    if not faiss_index_instance and not structured_perforated_sheet_data and not structured_tole_data:
        # Attempt to initialize if not already done by a direct call above
        initialize_retriever(force_reload=True)

    if not faiss_index_instance and not structured_perforated_sheet_data and not structured_tole_data:
        print("\nRetriever components still not loaded after attempt. Cannot run tests.")
        print("This might be because 'main_rag_index.index' or CSV files do not exist.")
        print("Please run 'src/rag/build_main_index.py' first.")
    else:
        test_queries = [
            "How do I create a simple box?",
            "Generate a FreeCAD script for a Perforated sheet R12 U27.72, length 100, width 50, thickness 1.",
            "Make a Tole part named 'Bracket A1' with parameters for bending.", # This won't find structured data by 'Bracket A1'
            "Example of Part.fuse operation",
            "User wants a sphere with radius 10"
        ]

        for t_query in test_queries:
            print(f"\n--- Query: '{t_query}' ---")
            retrieved_docs = retrieve_context(t_query, k_semantic=5)

            if retrieved_docs:
                print(f"  Retrieved {len(retrieved_docs)} documents:")
                for i, doc in enumerate(retrieved_docs):
                    print(f"    Doc {i+1}:")
                    print(f"      Content (first 100 chars): {doc.page_content[:100].replace(chr(10), ' ')}...")
                    print(f"      Metadata: {doc.metadata}")
            else:
                print("    No documents retrieved.")

    print("\nRAG Retriever Test Finished.")
