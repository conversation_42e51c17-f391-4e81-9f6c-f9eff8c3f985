# -*- coding: utf-8 -*-
"""
Image Chat Schemas

Pydantic models for Image chat API requests and responses.
"""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class ImageChatRequest(BaseModel):
    """Request model for Image chat processing."""
    session_id: Optional[str] = Field(None, description="Session ID for chat continuity")
    message: str = Field(..., description="User message/question about the image")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_abc123_456789",
                "message": "What type of perforated sheet is shown in this image?"
            }
        }


class ImageChatResponse(BaseModel):
    """Response model for Image chat processing."""
    success: bool = Field(..., description="Whether the processing was successful")
    message: str = Field(..., description="Response message from the chat system")
    session_id: str = Field(..., description="Session ID for this interaction")
    analysis_result: Optional[str] = Field(None, description="Image analysis result")
    cad_generated: bool = Field(False, description="Whether CAD was generated")
    obj_export: Optional[str] = Field(None, description="URL to OBJ file if generated")
    step_export: Optional[str] = Field(None, description="URL to STEP file if generated")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Image analyzed successfully. Found perforated sheet with circular holes.",
                "session_id": "session_abc123_456789",
                "analysis_result": "Class: perforated sheet, Name/code: PS-CIRCULAR-3MM",
                "cad_generated": True,
                "obj_export": "/download/obj/session_abc123_456789_latest.obj",
                "step_export": "/download/step/session_abc123_456789_latest.step",
                "timestamp": "2024-01-15T10:30:00"
            }
        }


class ImageUploadChatRequest(BaseModel):
    """Request model for Image upload and chat processing."""
    message: str = Field(..., description="User message/question about the image")
    session_id: Optional[str] = Field(None, description="Session ID for chat continuity")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Analyze this image and create a 3D model",
                "session_id": "session_abc123_456789"
            }
        }


class ImageChatHistoryResponse(BaseModel):
    """Response model for Image chat history."""
    session_id: str = Field(..., description="Session ID")
    messages: list = Field(..., description="List of chat messages in this session")
    total_messages: int = Field(..., description="Total number of messages")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_abc123_456789",
                "messages": [
                    {
                        "timestamp": "2024-01-15T10:30:00",
                        "user_message": "Analyze this image",
                        "response": "Image analyzed successfully",
                        "cad_generated": True
                    }
                ],
                "total_messages": 1
            }
        }


class SupportedImageFormats(BaseModel):
    """Response model for supported image formats."""
    formats: list[str] = Field(..., description="List of supported image formats")
    max_file_size: int = Field(..., description="Maximum file size in bytes")
    
    class Config:
        json_schema_extra = {
            "example": {
                "formats": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
                "max_file_size": 20971520
            }
        }
