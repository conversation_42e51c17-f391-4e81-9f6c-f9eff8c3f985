# -*- coding: utf-8 -*-
"""
PDF Chat Schemas

Pydantic models for PDF chat API requests and responses.
"""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class PDFChatRequest(BaseModel):
    """Request model for PDF chat processing."""
    session_id: Optional[str] = Field(None, description="Session ID for chat continuity")
    message: str = Field(..., description="User message/question about the PDF")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_abc123_456789",
                "message": "What are the dimensions of the perforated sheet in this PDF?"
            }
        }


class PDFChatResponse(BaseModel):
    """Response model for PDF chat processing."""
    success: bool = Field(..., description="Whether the processing was successful")
    message: str = Field(..., description="Response message from the chat system")
    session_id: str = Field(..., description="Session ID for this interaction")
    analysis_result: Optional[str] = Field(None, description="PDF analysis result")
    cad_generated: bool = Field(False, description="Whether CAD was generated")
    obj_export: Optional[str] = Field(None, description="URL to OBJ file if generated")
    step_export: Optional[str] = Field(None, description="URL to STEP file if generated")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "PDF analyzed successfully. Found perforated sheet with 5mm holes.",
                "session_id": "session_abc123_456789",
                "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
                "cad_generated": True,
                "obj_export": "/download/obj/session_abc123_456789_latest.obj",
                "step_export": "/download/step/session_abc123_456789_latest.step",
                "timestamp": "2024-01-15T10:30:00"
            }
        }


class PDFUploadChatRequest(BaseModel):
    """Request model for PDF upload and chat processing."""
    message: str = Field(..., description="User message/question about the PDF")
    session_id: Optional[str] = Field(None, description="Session ID for chat continuity")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Analyze this PDF and create a 3D model",
                "session_id": "session_abc123_456789"
            }
        }


class PDFChatHistoryResponse(BaseModel):
    """Response model for PDF chat history."""
    session_id: str = Field(..., description="Session ID")
    messages: list = Field(..., description="List of chat messages in this session")
    total_messages: int = Field(..., description="Total number of messages")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_abc123_456789",
                "messages": [
                    {
                        "timestamp": "2024-01-15T10:30:00",
                        "user_message": "Analyze this PDF",
                        "response": "PDF analyzed successfully",
                        "cad_generated": True
                    }
                ],
                "total_messages": 1
            }
        }
