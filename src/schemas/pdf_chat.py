# -*- coding: utf-8 -*-
"""
PDF Chat Schemas

Pydantic models for PDF chat API requests and responses.
"""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class PDFChatRequest(BaseModel):
    """Request model for PDF chat processing."""
    session_id: Optional[str] = Field(None, description="Session ID for chat continuity")
    message: str = Field(..., description="User message/question about the PDF")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_abc123_456789",
                "message": "What are the dimensions of the perforated sheet in this PDF?"
            }
        }


class PDFAnalysisResponse(BaseModel):
    """Response model for PDF analysis (Step 1)."""
    success: bool = Field(..., description="Whether the analysis was successful")
    message: str = Field(..., description="Analysis message")
    session_id: str = Field(..., description="Session ID for this interaction")
    class_type: Optional[str] = Field(None, description="Detected class (perforated sheet/tole)")
    name_code: Optional[str] = Field(None, description="Detected name/code")
    analysis_result: Optional[str] = Field(None, description="Full analysis result")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "PDF analyzed successfully",
                "session_id": "session_abc123_456789",
                "class_type": "perforated sheet",
                "name_code": "PS-5MM-STEEL",
                "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
                "timestamp": "2024-01-15T10:30:00"
            }
        }

class PDFCADRequest(BaseModel):
    """Request model for PDF CAD generation (Step 2)."""
    session_id: str = Field(..., description="Session ID from analysis step")
    analysis_result: str = Field(..., description="Analysis result from step 1")
    additional_instructions: Optional[str] = Field(None, description="Additional instructions for CAD generation")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "session_abc123_456789",
                "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
                "additional_instructions": "Make it 200x100x5mm with 5mm holes"
            }
        }

class PDFCADResponse(BaseModel):
    """Response model for PDF CAD generation (Step 2)."""
    success: bool = Field(..., description="Whether CAD generation was successful")
    message: str = Field(..., description="CAD generation message")
    session_id: str = Field(..., description="Session ID for this interaction")
    cad_generated: bool = Field(False, description="Whether CAD was generated")
    code: Optional[str] = Field(None, description="Generated FreeCAD code")
    obj_export: Optional[str] = Field(None, description="URL to OBJ file if generated")
    step_export: Optional[str] = Field(None, description="URL to STEP file if generated")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "CAD generated successfully",
                "session_id": "session_abc123_456789",
                "cad_generated": True,
                "code": "import FreeCAD\nimport Part\n# Generated FreeCAD code...",
                "obj_export": "/download/obj/session_abc123_456789_latest.obj",
                "step_export": "/download/step/session_abc123_456789_latest.step",
                "timestamp": "2024-01-15T10:30:00"
            }
        }


class PDFProcessResponse(BaseModel):
    """Response model for complete PDF processing (analyze + CAD generation)."""
    success: bool = Field(..., description="Whether the processing was successful")
    message: str = Field(..., description="Processing message")
    session_id: str = Field(..., description="Session ID for this interaction")
    class_type: Optional[str] = Field(None, description="Detected class (perforated sheet/tole)")
    name_code: Optional[str] = Field(None, description="Detected name/code")
    analysis_result: Optional[str] = Field(None, description="Full analysis result")
    cad_generated: bool = Field(False, description="Whether CAD was generated")
    code: Optional[str] = Field(None, description="Generated FreeCAD code")
    obj_export: Optional[str] = Field(None, description="URL to OBJ file if generated")
    step_export: Optional[str] = Field(None, description="URL to STEP file if generated")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "PDF processed successfully and CAD generated",
                "session_id": "session_abc123_456789",
                "class_type": "perforated sheet",
                "name_code": "PS-200x200x2",
                "analysis_result": "Class: perforated sheet, Name/code: PS-200x200x2",
                "cad_generated": True,
                "code": "import FreeCAD\nimport Part\n# Generated FreeCAD code...",
                "obj_export": "/download/obj/session_abc123_456789_latest.obj",
                "step_export": "/download/step/session_abc123_456789_latest.step",
                "timestamp": "2024-01-15T10:30:00"
            }
        }


class ChatMessage(BaseModel):
    """Individual chat message model."""
    timestamp: str = Field(..., description="Message timestamp")
    role: str = Field(..., description="Message role: 'human' or 'ai'")
    content: str = Field(..., description="Message content")

class PDFChatHistoryResponse(BaseModel):
    """Response model for PDF chat history."""
    session_id: str = Field(..., description="Session ID")
    messages: list[ChatMessage] = Field(..., description="List of chat messages in this session")
    total_messages: int = Field(..., description="Total number of messages")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "aaad7848-8398-49a7-ae73-60d9f500c1ec",
                "messages": [
                    {
                        "timestamp": "2025-05-23 02:56:20.876646",
                        "role": "human",
                        "content": "Analyze this PDF and create a 3D model"
                    },
                    {
                        "timestamp": "2025-05-23 02:56:20.884813",
                        "role": "ai",
                        "content": "PDF analyzed successfully. Found perforated sheet with 5mm holes."
                    }
                ],
                "total_messages": 2
            }
        }
