import re
import logging
import os
from typing import Dict, Tu<PERSON>, Optional, List
from urllib.parse import urlparse
from openai import OpenAI

logger = logging.getLogger(__name__)

class WebSearchProcessor:
    """
    Processor for detecting URLs in user input and extracting web content
    using OpenAI's web search tool.
    """
    
    def __init__(self):
        """Initialize the WebSearchProcessor with OpenAI client."""
        try:
            self.client = OpenAI()
            logger.info("WebSearchProcessor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            self.client = None
    
    def detect_urls(self, text: str) -> List[str]:
        """
        Detect URLs in the given text.
        
        Args:
            text (str): Input text to scan for URLs
            
        Returns:
            List[str]: List of detected URLs
        """
        # Enhanced URL regex pattern to handle complex URLs with special characters
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+(?:[^\s<>"{}|\\^`\[\].,!?;:])'
        urls = re.findall(url_pattern, text)
        
        # Also check for common URL patterns without protocol
        no_protocol_pattern = r'(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:/[^\s]*)?'
        potential_urls = re.findall(no_protocol_pattern, text)
        
        # Add http:// to URLs without protocol
        for url in potential_urls:
            if not url.startswith(('http://', 'https://')):
                full_url = f'https://{url}'
                if self._is_valid_url(full_url) and full_url not in urls:
                    urls.append(full_url)
        
        return urls
    
    def _is_valid_url(self, url: str) -> bool:
        """
        Validate if a string is a valid URL.
        
        Args:
            url (str): URL to validate
            
        Returns:
            bool: True if valid URL, False otherwise
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def extract_web_content(self, url: str, user_context: str = "") -> Dict[str, str]:
        """
        Extract content from a web URL using OpenAI's web search tool.
        
        Args:
            url (str): URL to extract content from
            user_context (str): Additional context about what user wants from the URL
            
        Returns:
            Dict[str, str]: Dictionary containing extracted content and metadata
        """
        if not self.client:
            return {
                "error": "OpenAI client not initialized",
                "content": "",
                "url": url
            }
        
        try:
            # Construct intelligent prompt for web content extraction - short format
            prompt = f"""
Access link {url} and analyze this product page to identify if it shows a 'Perforated sheet' or 'Tole'. 

Return in this exact format: 'Class: [perforated sheet/tole], Name/code: [value]'. 

If you can see specific dimensions, hole patterns, or model codes, include them briefly. 

Examples:
- Class: perforated sheet, Name/code: R12-U2772 (12mm holes, 27mm pitch, stainless steel)
- Class: tole, Name/code: AL-5754 (5mm thick aluminum sheet)

No other text or explanations.
"""
            
            logger.info(f"Extracting content from URL: {url}")
            
            response = self.client.responses.create(
                model="gpt-4.1-mini-2025-04-14",
                tools=[{"type": "web_search_preview"}],
                input=prompt
            )
            
            extracted_content = response.output_text
            
            return {
                "success": True,
                "content": extracted_content,
                "url": url,
                "user_context": user_context
            }
            
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {e}")
            return {
                "error": f"Error accessing link: {str(e)}",
                "content": "",
                "url": url
            }
    
    def process_text_with_urls(self, text: str) -> Tuple[bool, str, Dict]:
        """
        Process text that may contain URLs, extract web content if found.
        
        Args:
            text (str): Input text to process
            
        Returns:
            Tuple[bool, str, Dict]: 
                - bool: True if URLs were found and processed
                - str: Enhanced text with web content included
                - Dict: Metadata about the web search process
        """
        urls = self.detect_urls(text)
        
        if not urls:
            return False, text, {"urls_found": 0}
        
        logger.info(f"Found {len(urls)} URLs in text: {urls}")
        
        enhanced_text = text
        web_contents = []
        successful_extractions = 0
        
        for url in urls:
            # Use AI to determine what information to extract from this URL
            extraction_context = self._analyze_extraction_intent(text, url)
            
            # Extract web content with AI-determined context
            result = self.extract_web_content(url, extraction_context)
            
            if result.get("success") and result.get("content"):
                web_content = result["content"]
                web_contents.append({
                    "url": url,
                    "content": web_content,
                    "context": extraction_context
                })
                
                # Append web content to original text
                enhanced_text += f"\n\n[Information from {url}]:\n{web_content}"
                successful_extractions += 1
                
                logger.info(f"Successfully extracted content from {url}")
            else:
                error_msg = result.get("error", "Unknown error")
                logger.warning(f"Failed to extract content from {url}: {error_msg}")
                enhanced_text += f"\n\n[Error accessing {url}]: {error_msg}"
        
        metadata = {
            "urls_found": len(urls),
            "successful_extractions": successful_extractions,
            "urls_processed": urls,
            "web_contents": web_contents
        }
        
        return True, enhanced_text, metadata
    
    def _analyze_extraction_intent(self, text: str, url: str) -> str:
        """
        Use AI to analyze what specific information should be extracted from the URL.
        
        Args:
            text (str): Full user text
            url (str): URL to analyze
            
        Returns:
            str: AI-determined context for what to extract
        """
        if not self.client:
            # Fallback to simple context extraction
            return self._extract_context_around_url(text, url)
        
        try:
            intent_prompt = f"""
Analyze the user's request and determine the specific information to extract from the URL:

User's text: "{text}"
URL to access: {url}

Please determine:
1. What kind of information does the user want from this URL?
2. What will that information be used for?
3. Are there any technical, specification, or dimension factors to pay attention to?

Answer briefly (1-2 sentences) about the type of information to extract:
"""
            
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": intent_prompt}],
                max_tokens=100,
                temperature=0.3
            )
            
            extraction_intent = response.choices[0].message.content.strip()
            logger.info(f"AI extraction intent for {url}: {extraction_intent}")
            return extraction_intent
            
        except Exception as e:
            logger.warning(f"Error in AI intent analysis, using fallback: {e}")
            return self._extract_context_around_url(text, url)
    
    def _extract_context_around_url(self, text: str, url: str) -> str:
        """
        Fallback method to extract context around a URL in the text.
        
        Args:
            text (str): Full text
            url (str): URL to find context for
            
        Returns:
            str: Context around the URL
        """
        try:
            # Find the position of the URL in text
            url_pos = text.find(url)
            if url_pos == -1:
                return ""
            
            # Extract words before and after the URL
            start = max(0, url_pos - 50)  # 50 chars before
            end = min(len(text), url_pos + len(url) + 50)  # 50 chars after
            
            context = text[start:end].strip()
            
            # Remove the URL itself from context to avoid duplication
            context = context.replace(url, "").strip()
            
            return context
            
        except Exception as e:
            logger.warning(f"Error extracting context for URL {url}: {e}")
            return ""
    
    def _smart_fallback_analysis(self, text: str, urls: List[str]) -> bool:
        """
        Smart fallback analysis when AI is not available.
        Uses pattern matching to determine web search intent.
        
        Args:
            text (str): Text to analyze
            urls (List[str]): URLs found in text
            
        Returns:
            bool: True if likely needs web search
        """
        if not urls:
            return False
        
        text_lower = text.lower()
        
        # Strong positive indicators (definitely need web search)
        strong_positive = [
            "access link", "visit", "get information from", "extract from",
            "based on information from", "according to specs from", "from page",
            "check", "view information", "get info from", "based on",
            "extract from", "check", "specifications từ", "specs từ"
        ]
        
        # Negative indicators (likely don't need web search)
        negative_indicators = [
            "my website", "website of", "company website",
            "yesterday", "yesterday", "like page", "like the site",
            "my website", "our site", "my company", "homepage"
        ]
        
        # Task-oriented indicators (suggest web search for building/creating)
        task_indicators = [
            "create model", "create model", "build", "design",
            "make", "manufacture", "3d", "dimension", "specification"
        ]
        
        # Check for strong positive indicators
        positive_score = sum(1 for indicator in strong_positive if indicator in text_lower)
        
        # Check for negative indicators
        negative_score = sum(1 for indicator in negative_indicators if indicator in text_lower)
        
        # Check for task indicators
        task_score = sum(1 for indicator in task_indicators if indicator in text_lower)
        
        # Decision logic
        if positive_score > 0:
            return True  # Explicit web search request
        
        if negative_score > 0:
            return False  # URL mentioned for other purposes
        
        if task_score > 0 and len(urls) == 1:
            # Single URL + task = likely want info from that URL
            return True
            
        # If multiple URLs with no clear intent, be conservative
        if len(urls) > 2:
            return False
        
        # Default for single/few URLs with task context
        result = task_score > 0
        
        logger.info(f"Smart fallback decision: positive={positive_score}, negative={negative_score}, task={task_score} -> {result}")
        return result
    
    def is_web_search_request(self, text: str) -> bool:
        """
        Use AI to intelligently analyze if the text indicates a web search request.
        
        Args:
            text (str): Text to analyze
            
        Returns:
            bool: True if AI determines web search is needed
        """
        # First check if there are any URLs at all
        urls = self.detect_urls(text)
        if not urls:
            return False
        
        # If no OpenAI client, fallback to basic detection
        if not self.client:
            logger.warning("OpenAI client not available, using fallback URL detection")
            return bool(urls)
        
        try:
            # Use AI to analyze intent
            analysis_prompt = f"""
Analyze the following text and decide whether to access the URLs within it to retrieve supporting information:

Text: "{text}"

URLs được tìm thấy: {urls}

Please consider:
1. Does the user truly want information from these URLs?
2. Will accessing the URL help with their request?
3. Does the context indicate that information from the web is needed?

Answer only "YES" if the URL should be accessed, "NO" if not needed.
No further explanation, just answer YES or NO.
"""
            
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": analysis_prompt}],
                max_tokens=10,
                temperature=0
            )
            
            decision = response.choices[0].message.content.strip().upper()
            should_search = decision.startswith("YES")
            
            logger.info(f"AI decision for web search: {decision} -> {should_search}")
            return should_search
            
        except Exception as e:
            logger.warning(f"Error in AI web search analysis, using smart fallback: {e}")
            # Smart fallback: analyze text patterns for web search intent
            return self._smart_fallback_analysis(text, urls) 