# -*- coding: utf-8 -*-
"""
Image Handler Module

This module provides functionality to process image files using OpenAI Vision API.
Supports various image formats and integrates with the CAD generation system.
"""

import os
import base64
import tempfile
import logging
from pathlib import Path
from typing import Tuple, Optional, Any
from dotenv import load_dotenv
from openai import OpenAI
from sqlalchemy.orm import Session # Added import
import uuid # Added for session_id generation
import random # Added for session_id generation

logger = logging.getLogger(__name__)

try:
    from src.core.text_to_cad_agent import TextToCADAgent
    from src.crud.sessions import add_chat_history_entry # create_session and SessionModel removed from here
    from src.schemas.sessions import ChatRequest as ApiChatRequest # For chat_request_obj
    # SessionModel removed from here
except ImportError:
    logger.warning("TextToCADAgent, add_chat_history_entry, or ApiChatRequest not available for import in image_handler.py") # Adjusted warning
    TextToCADAgent = None
    add_chat_history_entry = None
    # create_session and SessionModel no longer set to None here as they are imported locally
    ApiChatRequest = None

class ImageProcessor:
    """
    Handles processing of image files using OpenAI's Vision API.

    This class provides functionality to:
    1. Process various image formats (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
    2. Convert images to base64 for API transmission
    3. Analyze images with optional custom prompts
    4. Integrate with CAD generation system
    """

    SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB

    def __init__(self, api_key: str = None, cad_agent: Optional[Any] = None):
        """
        Initialize the image processor with optional API key and CAD agent.

        Args:
            api_key: Optional OpenAI API key. If not provided, will look for
                    OPENAI_API_KEY environment variable.
            cad_agent: Optional instance of TextToCADAgent for CAD generation.
        """
        load_dotenv()

        self.api_key = api_key or os.getenv("OPENAI_API_KEY")

        if self.api_key:
            try:
                self.client = OpenAI(api_key=self.api_key)
                logger.info("OpenAI client initialized successfully for image processing")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
                self.client = None
        else:
            logger.warning("No OpenAI API key provided for image processing")
            self.client = None

        self.cad_agent = cad_agent
        if self.cad_agent and TextToCADAgent and isinstance(self.cad_agent, TextToCADAgent):
            logger.info("TextToCADAgent instance provided to ImageProcessor.")
        elif cad_agent:
            logger.warning("A cad_agent was provided, but TextToCADAgent class was not imported or type mismatch.")
        else:
            logger.info("TextToCADAgent instance not provided to ImageProcessor. Will only perform image analysis.")

    def _generate_session_id(self) -> str:
        """Generates a unique session ID."""
        rand_digits = random.randint(100000, 999999)
        rand_uuid_hex = uuid.uuid4().hex[:6]
        return f"session_{rand_uuid_hex}_{rand_digits}"

    def _parse_analysis_result(self, analysis_text: str) -> tuple[Optional[str], Optional[str]]:
        """
        Parse analysis result to extract class and name/code.

        Args:
            analysis_text: The analysis text from OpenAI

        Returns:
            Tuple of (class_type, name_code)
        """
        import re

        class_type = None
        name_code = None

        # Look for "Class: [value]" pattern
        class_match = re.search(r'Class:\s*([^,\n]+)', analysis_text, re.IGNORECASE)
        if class_match:
            class_type = class_match.group(1).strip()

        # Look for "Name/code: [value]" pattern
        name_match = re.search(r'Name/code:\s*([^,\n]+)', analysis_text, re.IGNORECASE)
        if name_match:
            name_code = name_match.group(1).strip()

        return class_type, name_code

    def analyze_image_only(self, db: Session, session_id: str, file_path: str, user_input: str = "") -> Tuple[bool, str, Optional[str], Optional[str]]:
        """
        Analyze an image file using OpenAI's Vision API without generating CAD.

        Args:
            db: SQLAlchemy database session.
            session_id: The session ID for this interaction.
            file_path: Path to the image file.
            user_input: Optional user input to send along with the default prompt.

        Returns:
            Tuple of (success: bool, analysis_text: str, class_type: Optional[str], name_code: Optional[str])
        """
        if not self.client:
            return False, "OpenAI client not initialized. Check API key.", None, None

        try:
            logger.info(f"Analyzing image: {file_path}")

            # Validate image file
            is_valid, error_message = self._validate_image_file(file_path)
            if not is_valid:
                return False, error_message, None, None

            # Ensure session exists in the database
            try:
                from src.crud.sessions import create_session
                from src.models.sessions import Session as SessionModel
                if db and session_id:
                    try:
                        session_name = Path(file_path).name[:50]
                        existing_session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
                        if not existing_session:
                            create_session(db, session_id, session_name)
                            logger.info(f"Created session {session_id} ('{session_name}') in database from image_handler.")
                    except Exception as e_session:
                        logger.error(f"Failed to ensure session {session_id} exists in database: {e_session}")
            except ImportError as e_import_local:
                logger.error(f"Failed to import create_session or SessionModel locally in analyze_image_only: {e_import_local}")

            # Encode image to base64
            base64_image = self._encode_image_to_base64(file_path)

            # Prepare prompt
            default_prompt = "Analyze this image and identify if it shows a 'Perforated sheet' or 'Tole'. Return in this exact format: 'Class: [perforated sheet/tole], Name/code: [value]'. If you can see specific dimensions, hole patterns, or model codes, include them. No other text."
            prompt = default_prompt

            if user_input and user_input.strip():
                prompt = f"{default_prompt} Additional context: {user_input}"

            logger.info(f"Using prompt for image analysis: {prompt}")

            # Create chat completion with the image
            completion = self.client.chat.completions.create(
                model="gpt-4.1-2025-04-14",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ],
                max_tokens=500
            )

            analysis_text = completion.choices[0].message.content
            logger.info(f"Successfully analyzed image. Response length: {len(analysis_text)}")

            # Parse the analysis result
            class_type, name_code = self._parse_analysis_result(analysis_text)

            # Save analysis to chat history (without CAD generation)
            if add_chat_history_entry and db and session_id:
                try:
                    user_message_for_db = f"Image analysis request: {user_input if user_input else 'Analyze image'}"
                    analysis_result = {"message": analysis_text, "class_type": class_type, "name_code": name_code}

                    chat_request_details = ApiChatRequest(
                        message=user_message_for_db,
                        image_path=file_path,
                        session_id=session_id
                    ) if ApiChatRequest else None

                    add_chat_history_entry(
                        db=db,
                        session_id=session_id,
                        user_message=user_message_for_db,
                        agent_result=analysis_result,
                        chat_request_obj=chat_request_details
                    )
                    logger.info(f"Saved image analysis to chat history for session {session_id}")
                except Exception as db_e:
                    logger.error(f"Failed to save image analysis to chat history for session {session_id}: {db_e}")

            return True, analysis_text, class_type, name_code

        except Exception as e:
            logger.exception(f"Error analyzing image for session {session_id}: {e}")
            if add_chat_history_entry and db and session_id:
                try:
                    add_chat_history_entry(db, session_id, f"Image analysis attempt for {file_path}", {"error": str(e), "message": f"Error analyzing image: {str(e)}"})
                except Exception as db_e:
                    logger.error(f"Failed to save error to chat history for session {session_id}: {db_e}")
            return False, f"Error analyzing image: {str(e)}", None, None

    def _validate_image_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Validate image file format and size.

        Args:
            file_path: Path to the image file

        Returns:
            Tuple of (is_valid: bool, error_message: str)
        """
        if not os.path.exists(file_path):
            return False, f"File not found: {file_path}"

        file_path_obj = Path(file_path)
        file_extension = file_path_obj.suffix.lower()

        if file_extension not in self.SUPPORTED_FORMATS:
            return False, f"Unsupported image format: {file_extension}. Supported formats: {', '.join(self.SUPPORTED_FORMATS)}"

        file_size = os.path.getsize(file_path)
        if file_size > self.MAX_FILE_SIZE:
            return False, f"File size ({file_size} bytes) exceeds maximum allowed size ({self.MAX_FILE_SIZE} bytes)"

        return True, ""

    def _encode_image_to_base64(self, file_path: str) -> str:
        """
        Encode image file to base64 string.

        Args:
            file_path: Path to the image file

        Returns:
            Base64 encoded string of the image
        """
        with open(file_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def process_image(self, db: Session, session_id: str, file_path: str, user_input: str = "") -> Tuple[bool, str]:
        """
        Process an image file using OpenAI's Vision API, optionally generate CAD, and save to chat history.

        Args:
            db: SQLAlchemy database session.
            session_id: The session ID for this interaction.
            file_path: Path to the image file.
            user_input: Optional user input to send along with the default prompt.

        Returns:
            Tuple of (success: bool, result_message_for_user: str)
            If successful, result contains the analysis text or CAD generation result.
            If unsuccessful, result contains an error message.
        """
        if not self.client:
            return False, "OpenAI client not initialized. Check API key."

        try:
            logger.info(f"Processing image: {file_path}")

            # Validate image file
            is_valid, error_message = self._validate_image_file(file_path)
            if not is_valid:
                return False, error_message

            # Ensure session exists in the database - import create_session and SessionModel locally
            try:
                from src.crud.sessions import create_session
                from src.models.sessions import Session as SessionModel
                if db and session_id: # create_session and SessionModel are now imported locally
                    try:
                        # Use filename as session name, limit length
                        session_name = Path(file_path).name[:50]
                        existing_session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
                        if not existing_session:
                            create_session(db, session_id, session_name)
                            logger.info(f"Created session {session_id} ('{session_name}') in database from image_handler.")
                    except Exception as e_session:
                        logger.error(f"Failed to ensure session {session_id} exists in database: {e_session}")
                        # Optionally, decide if this is a critical failure, for now, we'll log and continue
            except ImportError as e_import_local:
                logger.error(f"Failed to import create_session or SessionModel locally in process_image: {e_import_local}")
                # This is a critical failure for session creation, might need to return error or handle differently

            # Encode image to base64
            base64_image = self._encode_image_to_base64(file_path)

            # Prepare prompt
            default_prompt = "Analyze this image and identify if it shows a 'Perforated sheet' or 'Tole'. Return in this exact format: 'Class: [perforated sheet/tole], Name/code: [value]'. If you can see specific dimensions, hole patterns, or model codes, include them. No other text."
            prompt = default_prompt

            if user_input and user_input.strip():
                prompt = f"{default_prompt} Additional context: {user_input}"

            logger.info(f"Using prompt for image analysis: {prompt}")

            image_analysis_text = ""
            cad_agent_output_dict = None
            final_user_message = ""

            # Create chat completion with the image
            try:
                completion = self.client.chat.completions.create(
                    model="gpt-4.1-2025-04-14", # Consider making model configurable
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_image}"
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": prompt
                                }
                            ]
                        }
                    ],
                    max_tokens=500
                )

                image_analysis_text = completion.choices[0].message.content
                logger.info(f"Successfully processed image. Response length: {len(image_analysis_text)}")

                user_message_for_db = f"Based on image analysis: {image_analysis_text}. {user_input if user_input else ''}"

                # Try to generate CAD if agent is available and result looks like a valid analysis
                if self.cad_agent and image_analysis_text and ("Class:" in image_analysis_text or "perforated" in image_analysis_text.lower() or "tole" in image_analysis_text.lower()):
                    try:
                        logger.info(f"Attempting to generate CAD from image analysis result for session {session_id}")
                        cad_agent_output_dict = self.cad_agent.process_request(
                            user_text=user_message_for_db, # Pass the combined text
                            is_edit_request=False,
                            request_origin='image_processing',
                            session_id=session_id # Pass session_id to agent
                        )

                        if cad_agent_output_dict.get("code"):
                            logger.info(f"Successfully generated CAD code from image analysis for session {session_id}")
                            final_user_message = f"Image Analysis: {image_analysis_text}\n\nCAD Generation: {cad_agent_output_dict.get('message', 'Code generated successfully.')}"
                        else:
                            logger.info(f"CAD generation failed or returned questions for session {session_id}, returning image analysis only")
                            final_user_message = f"Image Analysis: {image_analysis_text}"
                            if cad_agent_output_dict.get("message"):
                                final_user_message += f"\n\nCAD Generation: {cad_agent_output_dict['message']}"
                            # If cad_agent_output_dict is None or no message, final_user_message remains just image_analysis_text + CAD gen message
                    except Exception as e:
                        logger.warning(f"CAD generation failed for image analysis in session {session_id}: {e}")
                        final_user_message = f"Image Analysis: {image_analysis_text}\n\nNote: CAD generation encountered an issue: {str(e)}"
                        # Construct a simple agent_result for db logging
                        cad_agent_output_dict = {"error": f"CAD generation encountered an issue: {str(e)}", "message": final_user_message}
                else: # No CAD agent or analysis not suitable for CAD
                    final_user_message = image_analysis_text
                    # Construct a simple agent_result for db logging if CAD was not attempted
                    cad_agent_output_dict = {"message": image_analysis_text} # No error, just the analysis

                # Save to chat history
                if add_chat_history_entry and db and session_id:
                    try:
                        # Create a minimal ChatRequest-like object for add_chat_history_entry
                        chat_request_details = ApiChatRequest(
                            message=user_message_for_db, # This is what the agent processed
                            image_path=file_path, # Store the path of the processed image
                            session_id=session_id
                            # Other fields will use defaults from ApiChatRequest or be None
                        ) if ApiChatRequest else None

                        add_chat_history_entry(
                            db=db,
                            session_id=session_id,
                            user_message=user_message_for_db, # The input to the agent/analysis
                            agent_result=cad_agent_output_dict, # Result from CAD agent or constructed
                            chat_request_obj=chat_request_details
                        )
                        logger.info(f"Saved image analysis/CAD attempt to chat history for session {session_id}")
                    except Exception as db_e:
                        logger.error(f"Failed to save image analysis to chat history for session {session_id}: {db_e}")
                        # Continue execution even if database save fails

                return True, final_user_message

            except Exception as e:
                logger.error(f"Error during image analysis for session {session_id}: {e}")
                # Try to save error to chat history, but don't fail if this fails too
                if add_chat_history_entry and db and session_id:
                    try:
                        add_chat_history_entry(db, session_id, f"Image analysis attempt for {file_path}", {"error": str(e), "message": f"Error processing image content: {str(e)}"})
                    except Exception as db_e:
                        logger.error(f"Failed to save error to chat history for session {session_id}: {db_e}")
                return False, f"Error processing image content: {str(e)}"

        except Exception as e:
            logger.exception(f"Error processing image for session {session_id}: {e}")
            # Try to save error to chat history, but don't fail if this fails too
            if add_chat_history_entry and db and session_id:
                try:
                    add_chat_history_entry(db, session_id, f"Image processing attempt for {file_path}", {"error": str(e), "message": f"Error processing image: {str(e)}"})
                except Exception as db_e:
                    logger.error(f"Failed to save error to chat history for session {session_id}: {db_e}")
            return False, f"Error processing image: {str(e)}"

    def process_image_from_bytes(self, db: Session, session_id: str, file_bytes: bytes, filename: str,
                               user_input: str = "") -> Tuple[bool, str]:
        """
        Process an image from bytes data using OpenAI's Vision API and save to chat history.

        Args:
            db: SQLAlchemy database session.
            session_id: The session ID for this interaction.
            file_bytes: Raw bytes of the image file.
            filename: Name to use for the temporary file.
            user_input: Optional user input to send along with the default prompt.

        Returns:
            Tuple of (success: bool, result_message_for_user: str)
        """
        if not self.client:
            return False, "OpenAI client not initialized. Check API key."

        temp_file = None
        try:
            # Create a temporary file
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            # Get file extension from filename
            file_extension = Path(filename).suffix.lower()
            if file_extension not in self.SUPPORTED_FORMATS:
                return False, f"Unsupported image format: {file_extension}"

            temp_file = tempfile.NamedTemporaryFile(delete=False,
                                                 dir=temp_dir,
                                                 suffix=file_extension,
                                                 prefix=f"{filename.replace(' ', '_')}_")

            # Write bytes to the temporary file
            temp_file.write(file_bytes)
            temp_file.close()

            # Process the temporary file
            success, result = self.process_image(db, session_id, temp_file.name, user_input)
            return success, result

        except Exception as e:
            logger.exception(f"Error processing image from bytes for session {session_id}: {e}")
            return False, f"Error processing image from bytes: {str(e)}"

        finally:
            # Clean up temporary file
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                    logger.info(f"Deleted temporary image file: {temp_file.name}")
                except Exception as e:
                    logger.warning(f"Failed to delete temporary image file: {e}")

    async def process_uploaded_file(self, db: Session, uploaded_file, user_input: str = "") -> Tuple[bool, str, Optional[str]]:
        """
        Process an uploaded image file from FastAPI's UploadFile and save to chat history.

        Args:
            db: SQLAlchemy database session.
            uploaded_file: The UploadFile object from FastAPI.
            user_input: Optional user input to send along with the default prompt.

        Returns:
            Tuple of (success: bool, result_message_for_user: str, session_id: Optional[str])
        """
        session_id = self._generate_session_id() # Generate a session ID for this processing
        logger.info(f"Generated session ID {session_id} for uploaded file {uploaded_file.filename}")

        if not self.client:
            return False, "OpenAI client not initialized. Check API key.", session_id

        try:
            # Create a temporary file to store the uploaded content
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            # Create a secure filename
            filename = uploaded_file.filename.replace(" ", "_").replace("/", "_")
            temp_file_path = temp_dir / filename

            # Save the uploaded file to disk
            content = await uploaded_file.read()

            # Validate file size
            if len(content) > self.MAX_FILE_SIZE:
                return False, f"File size ({len(content)} bytes) exceeds maximum allowed size ({self.MAX_FILE_SIZE} bytes)"

            with open(temp_file_path, "wb") as buffer:
                buffer.write(content)

            logger.info(f"Saved uploaded image file to: {temp_file_path}")

            # Process the file, passing db and session_id
            success, result = self.process_image(db, session_id, str(temp_file_path), user_input)

            # Clean up
            try:
                os.unlink(temp_file_path)
                logger.info(f"Deleted temporary image file: {temp_file_path}")
            except Exception as e:
                logger.warning(f"Failed to delete temporary image file: {e}")

            return success, result, session_id

        except Exception as e:
            logger.exception(f"Error processing uploaded image file: {e}")
            return False, f"Error processing uploaded image file: {str(e)}", session_id
