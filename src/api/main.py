import os
import logging
import time
from pathlib import Path
from typing import List, Optional

from fastapi import FastAP<PERSON>, Request, HTTPException, Depends
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy import or_

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tolery-api-test")

# Import database and models
try:
    from ..database.database import SessionLocal
    from ..models.sessions import Session as SessionModel, ChatHistory
    from ..crud import chat_processing as crud
    from ..core.chatbot import text_to_cad_agent
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import SessionLocal
    from src.models.sessions import Session as SessionModel, ChatHistory
    from src.crud import chat_processing as crud
    from src.core.chatbot import text_to_cad_agent

# Import routers
try:
    from .routes.pdf_chat import router as pdf_chat_router
    from .routes.image_chat import router as image_chat_router
except ImportError:
    from src.api.routes.pdf_chat import router as pdf_chat_router
    from src.api.routes.image_chat import router as image_chat_router

# FastAPI app
app = FastAPI(
    title="Tolery API-TEST",
    description="8 Core Endpoints for CAD Generation System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates and static files
project_root = Path(__file__).parent.parent.parent
templates_dir = project_root / "templates"
static_dir = project_root / "static"

templates = Jinja2Templates(directory=str(templates_dir))
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ============================================================================
# API-TEST: 8 CORE ENDPOINTS
# ============================================================================

# 1. SESSIONS MANAGEMENT
class SessionResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    session_name: str = Field(..., description="Session name")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: Optional[str] = Field(None, description="Last update timestamp")

class SessionListResponse(BaseModel):
    sessions: List[SessionResponse] = Field(..., description="List of sessions")
    total: int = Field(..., description="Total number of sessions")

@app.get("/api/sessions", response_model=SessionListResponse)
async def get_sessions(db: Session = Depends(get_db)):
    """Get all sessions."""
    try:
        sessions = db.query(SessionModel).order_by(SessionModel.created_at.desc()).all()

        session_list = []
        for session in sessions:
            session_list.append(SessionResponse(
                session_id=session.session_id,
                session_name=session.name or "Unnamed Session",
                created_at=session.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                updated_at=session.updated_at.strftime("%Y-%m-%d %H:%M:%S") if session.updated_at else None
            ))

        return SessionListResponse(sessions=session_list, total=len(session_list))

    except Exception as e:
        logger.error(f"Error getting sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting sessions: {str(e)}")

class CreateSessionRequest(BaseModel):
    session_name: str = Field(..., description="Session name")

@app.post("/api/sessions", response_model=SessionResponse)
async def create_session(request: CreateSessionRequest, db: Session = Depends(get_db)):
    """Create a new session."""
    try:
        from src.crud.sessions import create_session
        import uuid

        # Generate unique session_id
        session_id = f"session_{uuid.uuid4().hex[:6]}_{int(time.time() * 1000) % 1000000:06d}"

        # Create session
        create_session(db, session_id, request.session_name)

        # Get created session
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()

        return SessionResponse(
            session_id=session.session_id,
            session_name=session.name,
            created_at=session.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            updated_at=session.updated_at.strftime("%Y-%m-%d %H:%M:%S") if session.updated_at else None
        )

    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating session: {str(e)}")

# 2. SESSION INFO
class SessionInfoResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    session_name: str = Field(..., description="Session name")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: Optional[str] = Field(None, description="Last update timestamp")
    total_messages: int = Field(..., description="Total chat messages")
    latest_code: Optional[str] = Field(None, description="Latest generated code")

@app.get("/api/sessions/{session_id}", response_model=SessionInfoResponse)
async def get_session_info(session_id: str, db: Session = Depends(get_db)):
    """Get session information."""
    try:
        # Get session
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Get chat history count
        message_count = db.query(ChatHistory).filter(ChatHistory.session_id == session_id).count()

        # Get latest code
        latest_entry = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id,
            ChatHistory.lasted_code.isnot(None)
        ).order_by(ChatHistory.id.desc()).first()

        return SessionInfoResponse(
            session_id=session.session_id,
            session_name=session.name or "Unnamed Session",
            created_at=session.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            updated_at=session.updated_at.strftime("%Y-%m-%d %H:%M:%S") if session.updated_at else None,
            total_messages=message_count,
            latest_code=latest_entry.lasted_code if latest_entry else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting session info: {str(e)}")

# 3. DELETE SESSION
@app.delete("/api/sessions/{session_id}")
async def delete_session(session_id: str, db: Session = Depends(get_db)):
    """Delete a session."""
    try:
        # Check if session exists
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Delete chat history
        db.query(ChatHistory).filter(ChatHistory.session_id == session_id).delete()

        # Delete session
        db.delete(session)
        db.commit()

        return {"success": True, "message": f"Session {session_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")

# 4. GET EXPORT
class ExportResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    exports: List[dict] = Field(..., description="List of export files")

@app.get("/api/get-export", response_model=ExportResponse)
async def get_export(session_id: str, types: Optional[str] = None, db: Session = Depends(get_db)):
    """Get export files for a session."""
    try:
        # Get chat history with exports
        query = db.query(ChatHistory).filter(ChatHistory.session_id == session_id)

        if types:
            # Filter by export types
            type_list = [t.strip().lower() for t in types.split(',')]
            filters = []
            if 'obj' in type_list:
                filters.append(ChatHistory.obj_export.isnot(None))
            if 'step' in type_list:
                filters.append(ChatHistory.step_export.isnot(None))

            if filters:
                query = query.filter(or_(*filters))

        entries = query.order_by(ChatHistory.created_at.desc()).all()

        exports = []
        for entry in entries:
            export_item = {
                "id": entry.id,
                "created_at": entry.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "message": entry.message
            }

            if entry.obj_export:
                export_item["obj_export"] = entry.obj_export
            if entry.step_export:
                export_item["step_export"] = entry.step_export

            exports.append(export_item)

        return ExportResponse(session_id=session_id, exports=exports)

    except Exception as e:
        logger.error(f"Error getting exports: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting exports: {str(e)}")

# 5. CHAT HISTORY (Unified for PDF, Image, Chat)
class ChatHistoryResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    messages: List[dict] = Field(..., description="List of chat messages")
    total_messages: int = Field(..., description="Total number of messages")

@app.get("/api/chat-history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(session_id: str, db: Session = Depends(get_db)):
    """Retrieve chat history for any session (PDF, Image, or Chat)."""
    logger.info(f"Getting chat history for session: {session_id}")

    try:
        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append({
                    "timestamp": entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "human",
                    "content": entry.message
                })

            # Add AI response if available
            if entry.response:
                messages.append({
                    "timestamp": entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "ai",
                    "content": entry.response
                })
            elif entry.output:
                # Fallback to output if no response
                messages.append({
                    "timestamp": entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "ai",
                    "content": entry.output
                })

        return ChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")

# 6. CHAT (Regular text chat)
class ChatRequest(BaseModel):
    message: str = Field(..., description="The user's message/request")
    is_edit_request: bool = Field(False, description="Whether this is a request to edit existing code")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")

@app.post("/api/chat")
async def chat(request_data: ChatRequest, db: Session = Depends(get_db)):
    """Process a chat message and generate CAD code with session continuity."""
    user_message = request_data.message
    is_edit_request = request_data.is_edit_request
    session_id = request_data.session_id

    logger.info(f"Chat request received: '{user_message[:50]}...' (is_edit: {is_edit_request}, session_id: {session_id})")

    if not user_message:
        logger.warning("Empty message received")
        raise HTTPException(status_code=400, detail="No message provided")

    try:
        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=user_message,
            session_id=session_id,
            image_path="",
            part_file_name="part_file_name",
            export_format="obj",
            material_choice="STEEL",
            selected_feature_uuid="",
            is_edit_request=is_edit_request
        )

        # Process using our enhanced session handling in CRUD
        result = crud.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='web')

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing chat request: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        # Log success
        if "code" in result and result["code"]:
            logger.info(f"Successfully generated code ({len(result['code'])} characters)")

        # Return the result dictionary
        return result

    except Exception as e:
        logger.exception(f"Unexpected error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# Mount PDF and Image Chat routes (endpoints 7 & 8)
app.include_router(pdf_chat_router, prefix="/api", tags=["pdf-chat"])
app.include_router(image_chat_router, prefix="/api", tags=["image-chat"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8124)
