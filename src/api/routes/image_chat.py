# -*- coding: utf-8 -*-
"""
Image Chat API Routes

API endpoints for Image chat functionality with CAD integration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session

from ...database.database import get_db
from ...utils.image_handler import ImageProcessor
from ...schemas.image_chat import (
    ImageChatRequest,
    ImageAnalysisResponse,
    ImageCADRequest,
    ImageCADResponse,
    ImageUploadChatRequest,
    ImageChatHistoryResponse,
    SupportedImageFormats
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/image-chat",
    tags=["image-chat"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize Image processor with CAD agent
image_processor = None

def get_image_processor():
    """Get or create Image processor instance with CAD agent."""
    global image_processor
    if image_processor is None:
        try:
            # Import the initialized agent from chatbot module
            from ...core.chatbot import text_to_cad_agent
            image_processor = ImageProcessor(cad_agent=text_to_cad_agent)
            logger.info("Image processor initialized with CAD agent")
        except Exception as e:
            logger.error(f"Failed to initialize Image processor with CAD agent: {e}")
            image_processor = ImageProcessor()
            logger.info("Image processor initialized without CAD agent")
    return image_processor


@router.post("/analyze",
             summary="Upload and analyze image (Step 1)",
             description="Upload an image file and analyze it to extract class and name/code",
             response_model=ImageAnalysisResponse)
async def analyze_image(
    file: UploadFile = File(..., description="Image file to upload"),
    message: str = Form(..., description="Initial message/question about the image"),
    session_id: Optional[str] = Form(None, description="Optional session ID for continuity"),
    db: Session = Depends(get_db)
):
    """
    Upload an image file and analyze it to extract class and name/code (Step 1).

    Args:
        file: Uploaded image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
        message: Initial message/question about the image
        session_id: Optional session ID for chat continuity
        db: Database session

    Returns:
        ImageAnalysisResponse: Response with analysis result (class, name/code)
    """
    logger.info(f"Received image analysis request: {file.filename}")

    # Validate file is a supported image format
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
    file_extension = f'.{file_extension}'

    if file_extension not in supported_extensions:
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(
            status_code=400,
            detail=f"File must be an image. Supported formats: {', '.join(supported_extensions)}"
        )

    try:
        processor = get_image_processor()

        # Generate session_id if not provided or invalid
        if not session_id or session_id == "string":
            final_session_id = processor._generate_session_id()
        else:
            final_session_id = session_id

        # Save uploaded file temporarily
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Analyze image only (no CAD generation)
            success, analysis_text, class_type, name_code = processor.analyze_image_only(
                db=db,
                session_id=final_session_id,
                file_path=temp_file_path,
                user_input=message
            )

            if success:
                return ImageAnalysisResponse(
                    success=True,
                    message="Image analyzed successfully",
                    session_id=final_session_id,
                    class_type=class_type,
                    name_code=name_code,
                    analysis_result=analysis_text
                )
            else:
                return ImageAnalysisResponse(
                    success=False,
                    message=analysis_text,
                    session_id=final_session_id,
                    class_type=None,
                    name_code=None,
                    analysis_result=None
                )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error analyzing image: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing image: {str(e)}")


@router.post("/generate-cad",
             summary="Generate CAD from analysis result (Step 2)",
             description="Generate CAD code from image analysis result",
             response_model=ImageCADResponse)
async def generate_cad_from_analysis(
    request: ImageCADRequest,
    db: Session = Depends(get_db)
):
    """
    Generate CAD code from image analysis result (Step 2).

    Args:
        request: CAD generation request with analysis result
        db: Database session

    Returns:
        ImageCADResponse: Response with CAD generation result
    """
    logger.info(f"Received CAD generation request for session: {request.session_id}")

    try:
        processor = get_image_processor()

        if not processor.cad_agent:
            return ImageCADResponse(
                success=False,
                message="CAD agent not available",
                session_id=request.session_id,
                cad_generated=False
            )

        # Prepare input for CAD agent
        cad_input = request.analysis_result
        if request.additional_instructions:
            cad_input += f"\n\nAdditional instructions: {request.additional_instructions}"

        # Generate CAD using the chat tool (same as regular chat API)
        try:
            from ...schemas.sessions import ChatRequest as ApiChatRequest
            from ...crud.chat_processing import handle_chat_request
            from ...core.chatbot import text_to_cad_agent

            # Create ChatRequest for the tool chat
            chat_request = ApiChatRequest(
                message=cad_input,
                session_id=request.session_id,
                image_path="",
                part_file_name="image_generated_part",
                export_format="both",  # Generate both OBJ and STEP
                material_choice="STEEL",
                selected_feature_uuid="",
                is_edit_request=False
            )

            # Call the main chat tool
            result = handle_chat_request(
                db=db,
                chat_req=chat_request,
                agent=text_to_cad_agent,
                request_origin='image_api'
            )

            if result and hasattr(result, 'chat_response') and result.chat_response:
                return ImageCADResponse(
                    success=True,
                    message=result.chat_response,
                    session_id=request.session_id,
                    cad_generated=True,
                    code=getattr(result, 'code', None),
                    obj_export=getattr(result, 'obj_export', None),
                    step_export=getattr(result, 'step_export', None)
                )
            else:
                return ImageCADResponse(
                    success=False,
                    message="CAD generation failed",
                    session_id=request.session_id,
                    cad_generated=False
                )

        except Exception as cad_e:
            logger.error(f"Error in CAD generation: {cad_e}")
            return ImageCADResponse(
                success=False,
                message=f"CAD generation error: {str(cad_e)}",
                session_id=request.session_id,
                cad_generated=False
            )

    except Exception as e:
        logger.error(f"Error generating CAD from analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating CAD: {str(e)}")


@router.get("/history/{session_id}",
            summary="Get image chat history",
            description="Retrieve chat history for an image session",
            response_model=ImageChatHistoryResponse)
async def get_image_chat_history(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Retrieve chat history for an image session.

    Args:
        session_id: Session ID to get history for
        db: Database session

    Returns:
        ImageChatHistoryResponse: Chat history for the session
    """
    logger.info(f"Getting image chat history for session: {session_id}")

    try:
        from ...models.sessions import ChatHistory
        from ...schemas.image_chat import ChatMessage

        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append(ChatMessage(
                    timestamp=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="human",
                    content=entry.message
                ))

            # Add AI response if available
            if entry.response:
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.response
                ))
            elif entry.output:
                # Fallback to output if no response
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.output
                ))

        return ImageChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting image chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")


@router.get("/formats",
            summary="Get supported image formats",
            description="Get information about supported image formats and limitations",
            response_model=SupportedImageFormats)
async def get_image_formats():
    """
    Get information about supported image formats and limitations.

    Returns:
        SupportedImageFormats: Information about supported formats
    """
    return SupportedImageFormats(
        formats=[".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
        max_file_size=20 * 1024 * 1024  # 20MB
    )
