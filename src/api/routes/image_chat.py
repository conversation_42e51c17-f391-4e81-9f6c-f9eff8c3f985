# -*- coding: utf-8 -*-
"""
Image Chat API Routes

API endpoints for Image chat functionality with CAD integration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session

from ...database.database import get_db
from ...utils.image_handler import ImageProcessor
from ...schemas.image_chat import (
    ImageChatRequest,
    ImageProcessResponse,
    ImageChatHistoryResponse,
    SupportedImageFormats
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/image-chat",
    tags=["image-chat"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize Image processor with CAD agent
image_processor = None

def get_image_processor():
    """Get or create Image processor instance with CAD agent."""
    global image_processor
    if image_processor is None:
        try:
            # Import the initialized agent from chatbot module
            from ...core.chatbot import text_to_cad_agent
            image_processor = ImageProcessor(cad_agent=text_to_cad_agent)
            logger.info("Image processor initialized with CAD agent")
        except Exception as e:
            logger.error(f"Failed to initialize Image processor with CAD agent: {e}")
            image_processor = ImageProcessor()
            logger.info("Image processor initialized without CAD agent")
    return image_processor


@router.post("/process",
             summary="Upload Image and generate CAD (Complete Flow)",
             description="Upload an image file, analyze it, and generate CAD code in one step",
             response_model=ImageProcessResponse)
async def process_image_complete(
    file: UploadFile = File(..., description="Image file to upload"),
    message: str = Form(..., description="Initial message/question about the image"),
    session_id: Optional[str] = Form(None, description="Optional session ID for continuity"),
    db: Session = Depends(get_db)
):
    """
    Complete Image processing: Upload → Analyze → Generate CAD.

    Args:
        file: Uploaded image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
        message: Initial message/question about the image
        session_id: Optional session ID for chat continuity
        db: Database session

    Returns:
        ImageProcessResponse: Response with analysis result and CAD generation
    """
    logger.info(f"Received image complete processing request: {file.filename}")

    # Validate file is a supported image format
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
    file_extension = f'.{file_extension}'

    if file_extension not in supported_extensions:
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(
            status_code=400,
            detail=f"File must be an image. Supported formats: {', '.join(supported_extensions)}"
        )

    try:
        processor = get_image_processor()

        # Generate session_id if not provided or invalid
        if not session_id or session_id == "string":
            final_session_id = processor._generate_session_id()
        else:
            final_session_id = session_id

        # Save uploaded file temporarily
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Step 1: Analyze Image
            logger.info(f"Step 1: Analyzing image for session {final_session_id}")
            success, analysis_text, class_type, name_code = processor.analyze_image_only(
                db=db,
                session_id=final_session_id,
                file_path=temp_file_path,
                user_input=message
            )

            if not success:
                return ImageProcessResponse(
                    success=False,
                    message=f"Image analysis failed: {analysis_text}",
                    session_id=final_session_id,
                    class_type=None,
                    name_code=None,
                    analysis_result=None,
                    cad_generated=False
                )

            logger.info(f"Step 1 completed: class_type={class_type}, name_code={name_code}")

            # Step 2: Generate CAD using tool chat
            logger.info(f"Step 2: Generating CAD for session {final_session_id}")

            if not processor.cad_agent:
                return ImageProcessResponse(
                    success=True,
                    message="Image analyzed successfully but CAD agent not available",
                    session_id=final_session_id,
                    class_type=class_type,
                    name_code=name_code,
                    analysis_result=analysis_text,
                    cad_generated=False
                )

            # Prepare input for CAD generation
            cad_input = analysis_text
            if message and message.strip():
                cad_input += f"\n\nAdditional context: {message}"

            # Generate CAD using the chat tool
            try:
                from ...schemas.sessions import ChatRequest as ApiChatRequest
                from ...crud.chat_processing import handle_chat_request
                from ...core.chatbot import text_to_cad_agent

                # Create ChatRequest for the tool chat
                chat_request = ApiChatRequest(
                    message=cad_input,
                    session_id=final_session_id,
                    image_path="",
                    part_file_name="image_generated_part",
                    export_format="both",  # Generate both OBJ and STEP
                    material_choice="STEEL",
                    selected_feature_uuid="",
                    is_edit_request=False
                )

                # Call the main chat tool
                result = handle_chat_request(
                    db=db,
                    chat_req=chat_request,
                    agent=text_to_cad_agent,
                    request_origin='image_api'
                )

                if result and hasattr(result, 'chat_response') and result.chat_response:
                    logger.info(f"Step 2 completed: CAD generated successfully")
                    return ImageProcessResponse(
                        success=True,
                        message="Image processed successfully and CAD generated",
                        session_id=final_session_id,
                        class_type=class_type,
                        name_code=name_code,
                        analysis_result=analysis_text,
                        cad_generated=True,
                        code=getattr(result, 'code', None),
                        obj_export=getattr(result, 'obj_export', None),
                        step_export=getattr(result, 'step_export', None)
                    )
                else:
                    logger.warning(f"Step 2 failed: CAD generation failed")
                    return ImageProcessResponse(
                        success=True,
                        message="Image analyzed successfully but CAD generation failed",
                        session_id=final_session_id,
                        class_type=class_type,
                        name_code=name_code,
                        analysis_result=analysis_text,
                        cad_generated=False
                    )

            except Exception as cad_e:
                logger.error(f"Error in CAD generation: {cad_e}")
                return ImageProcessResponse(
                    success=True,
                    message=f"Image analyzed successfully but CAD generation error: {str(cad_e)}",
                    session_id=final_session_id,
                    class_type=class_type,
                    name_code=name_code,
                    analysis_result=analysis_text,
                    cad_generated=False
                )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error processing image: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")


@router.get("/history/{session_id}",
            summary="Get image chat history",
            description="Retrieve chat history for an image session",
            response_model=ImageChatHistoryResponse)
async def get_image_chat_history(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Retrieve chat history for an image session.

    Args:
        session_id: Session ID to get history for
        db: Database session

    Returns:
        ImageChatHistoryResponse: Chat history for the session
    """
    logger.info(f"Getting image chat history for session: {session_id}")

    try:
        from ...models.sessions import ChatHistory
        from ...schemas.image_chat import ChatMessage

        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append(ChatMessage(
                    timestamp=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="human",
                    content=entry.message
                ))

            # Add AI response if available
            if entry.response:
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.response
                ))
            elif entry.output:
                # Fallback to output if no response
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.output
                ))

        return ImageChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting image chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")


@router.get("/formats",
            summary="Get supported image formats",
            description="Get information about supported image formats and limitations",
            response_model=SupportedImageFormats)
async def get_image_formats():
    """
    Get information about supported image formats and limitations.

    Returns:
        SupportedImageFormats: Information about supported formats
    """
    return SupportedImageFormats(
        formats=[".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
        max_file_size=20 * 1024 * 1024  # 20MB
    )
