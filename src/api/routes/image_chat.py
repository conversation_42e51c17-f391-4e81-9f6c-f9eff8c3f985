# -*- coding: utf-8 -*-
"""
Image Chat API Routes

API endpoints for Image chat functionality with CAD integration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...utils.image_handler import ImageProcessor
from ...schemas.image_chat import (
    ImageChatRequest,
    ImageChatResponse,
    ImageUploadChatRequest,
    ImageChatHistoryResponse,
    SupportedImageFormats
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/image-chat",
    tags=["image-chat"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize Image processor with CAD agent
image_processor = None

def get_image_processor():
    """Get or create Image processor instance with CAD agent."""
    global image_processor
    if image_processor is None:
        try:
            from ...core.text_to_cad_agent import TextToCADAgent
            cad_agent = TextToCADAgent()
            image_processor = ImageProcessor(cad_agent=cad_agent)
            logger.info("Image processor initialized with CAD agent")
        except Exception as e:
            logger.error(f"Failed to initialize Image processor with CAD agent: {e}")
            image_processor = ImageProcessor()
            logger.info("Image processor initialized without CAD agent")
    return image_processor


@router.post("/upload",
             summary="Upload image and start chat",
             description="Upload an image file and start a chat session for analysis and CAD generation",
             response_model=ImageChatResponse)
async def upload_image_and_chat(
    file: UploadFile = File(..., description="Image file to upload"),
    message: str = Form(..., description="Initial message/question about the image"),
    session_id: Optional[str] = Form(None, description="Optional session ID for continuity"),
    db: Session = Depends(get_db)
):
    """
    Upload an image file and start a chat session for analysis and CAD generation.

    Args:
        file: Uploaded image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
        message: Initial message/question about the image
        session_id: Optional session ID for chat continuity
        db: Database session

    Returns:
        ImageChatResponse: Response with analysis result and CAD generation status
    """
    logger.info(f"Received image upload and chat request: {file.filename}")

    # Validate file is a supported image format
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
    file_extension = f'.{file_extension}'

    if file_extension not in supported_extensions:
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(
            status_code=400,
            detail=f"File must be an image. Supported formats: {', '.join(supported_extensions)}"
        )

    try:
        processor = get_image_processor()

        # Process the uploaded file
        success, response_message, generated_session_id = await processor.process_uploaded_file(
            db=db,
            uploaded_file=file,
            user_input=message
        )

        # Use provided session_id or the generated one
        final_session_id = session_id or generated_session_id

        if success:
            # Check if CAD was generated by looking for export URLs in the response
            cad_generated = "CAD Generation:" in response_message

            # Extract analysis result if available
            analysis_result = None
            if "Image Analysis:" in response_message:
                parts = response_message.split("Image Analysis:")
                if len(parts) > 1:
                    analysis_part = parts[1].split("\n\nCAD Generation:")[0].strip()
                    analysis_result = analysis_part

            # TODO: Extract actual export URLs from the CAD agent response
            # For now, we'll construct them based on session_id if CAD was generated
            obj_export = None
            step_export = None
            if cad_generated:
                obj_export = f"/download/obj/{final_session_id}_latest.obj"
                step_export = f"/download/step/{final_session_id}_latest.step"

            return ImageChatResponse(
                success=True,
                message=response_message,
                session_id=final_session_id,
                analysis_result=analysis_result,
                cad_generated=cad_generated,
                obj_export=obj_export,
                step_export=step_export
            )
        else:
            return ImageChatResponse(
                success=False,
                message=response_message,
                session_id=final_session_id,
                analysis_result=None,
                cad_generated=False
            )

    except Exception as e:
        logger.error(f"Error processing image upload and chat: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")


@router.post("/chat/{session_id}",
             summary="Continue image chat conversation",
             description="Continue chatting about a previously uploaded image",
             response_model=ImageChatResponse)
async def continue_image_chat(
    session_id: str,
    request: ImageChatRequest,
    db: Session = Depends(get_db)
):
    """
    Continue a chat conversation about a previously uploaded image.

    Args:
        session_id: Session ID for the chat
        request: Chat request with message
        db: Database session

    Returns:
        ImageChatResponse: Response with chat result
    """
    logger.info(f"Continuing image chat for session: {session_id}")

    try:
        # TODO: Implement chat continuation logic
        # This would involve:
        # 1. Retrieving the session context from database
        # 2. Processing the new message with context
        # 3. Potentially generating new CAD if requested

        # For now, return a placeholder response
        return ImageChatResponse(
            success=True,
            message=f"Chat continuation for session {session_id} with message: {request.message}",
            session_id=session_id,
            analysis_result=None,
            cad_generated=False
        )

    except Exception as e:
        logger.error(f"Error continuing image chat: {e}")
        raise HTTPException(status_code=500, detail=f"Error continuing chat: {str(e)}")


@router.get("/history/{session_id}",
            summary="Get image chat history",
            description="Retrieve chat history for an image session",
            response_model=ImageChatHistoryResponse)
async def get_image_chat_history(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Retrieve chat history for an image session.

    Args:
        session_id: Session ID to get history for
        db: Database session

    Returns:
        ImageChatHistoryResponse: Chat history for the session
    """
    logger.info(f"Getting image chat history for session: {session_id}")

    try:
        from ...models.sessions import ChatHistory
        from ...schemas.image_chat import ChatMessage

        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append(ChatMessage(
                    timestamp=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="human",
                    content=entry.message
                ))

            # Add AI response if available
            if entry.response:
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.response
                ))
            elif entry.output:
                # Fallback to output if no response
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.output
                ))

        return ImageChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting image chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")


@router.get("/formats",
            summary="Get supported image formats",
            description="Get information about supported image formats and limitations",
            response_model=SupportedImageFormats)
async def get_image_formats():
    """
    Get information about supported image formats and limitations.

    Returns:
        SupportedImageFormats: Information about supported formats
    """
    return SupportedImageFormats(
        formats=[".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
        max_file_size=20 * 1024 * 1024  # 20MB
    )
