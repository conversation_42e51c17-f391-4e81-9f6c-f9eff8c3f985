"""
API routes package for Tolery API.
PDF and Image chat routes are available separately for api-test.
"""
from .sessions import router as sessions_router
from .cad import router as cad_router
from .api_app import api_app

# PDF and Image chat routers are available for import but not included in api_app
from .pdf_chat import router as pdf_chat_router
from .image_chat import router as image_chat_router

__all__ = ["sessions_router", "cad_router", "api_app", "pdf_chat_router", "image_chat_router"]
