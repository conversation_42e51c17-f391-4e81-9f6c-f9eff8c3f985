# -*- coding: utf-8 -*-
"""
PDF Chat API Routes

API endpoints for PDF chat functionality with CAD integration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session

from ...database.database import get_db
from ...utils.pdf_handler import PDFProcessor
from ...schemas.pdf_chat import (
    PDFChatRequest,
    PDFAnalysisResponse,
    PDFCADRequest,
    PDFCADResponse,
    PDFUploadChatRequest,
    PDFChatHistoryResponse
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/pdf-chat",
    tags=["pdf-chat"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize PDF processor with CAD agent
pdf_processor = None

def get_pdf_processor():
    """Get or create PDF processor instance with CAD agent."""
    global pdf_processor
    if pdf_processor is None:
        try:
            # Import the initialized agent from chatbot module
            from ...core.chatbot import text_to_cad_agent
            pdf_processor = PDFProcessor(cad_agent=text_to_cad_agent)
            logger.info("PDF processor initialized with CAD agent")
        except Exception as e:
            logger.error(f"Failed to initialize PDF processor with CAD agent: {e}")
            pdf_processor = PDFProcessor()
            logger.info("PDF processor initialized without CAD agent")
    return pdf_processor


@router.post("/analyze",
             summary="Upload and analyze PDF (Step 1)",
             description="Upload a PDF file and analyze it to extract class and name/code",
             response_model=PDFAnalysisResponse)
async def analyze_pdf(
    file: UploadFile = File(..., description="PDF file to upload"),
    message: str = Form(..., description="Initial message/question about the PDF"),
    session_id: Optional[str] = Form(None, description="Optional session ID for continuity"),
    db: Session = Depends(get_db)
):
    """
    Upload a PDF file and analyze it to extract class and name/code (Step 1).

    Args:
        file: Uploaded PDF file
        message: Initial message/question about the PDF
        session_id: Optional session ID for chat continuity
        db: Database session

    Returns:
        PDFAnalysisResponse: Response with analysis result (class, name/code)
    """
    logger.info(f"Received PDF analysis request: {file.filename}")

    # Validate file is a PDF
    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        processor = get_pdf_processor()

        # Generate session_id if not provided or invalid
        if not session_id or session_id == "string":
            final_session_id = processor._generate_session_id()
        else:
            final_session_id = session_id

        # Save uploaded file temporarily
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Analyze PDF only (no CAD generation)
            success, analysis_text, class_type, name_code = processor.analyze_pdf_only(
                db=db,
                session_id=final_session_id,
                file_path=temp_file_path,
                user_input=message
            )

            if success:
                return PDFAnalysisResponse(
                    success=True,
                    message="PDF analyzed successfully",
                    session_id=final_session_id,
                    class_type=class_type,
                    name_code=name_code,
                    analysis_result=analysis_text
                )
            else:
                return PDFAnalysisResponse(
                    success=False,
                    message=analysis_text,
                    session_id=final_session_id,
                    class_type=None,
                    name_code=None,
                    analysis_result=None
                )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error analyzing PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing PDF: {str(e)}")


@router.post("/generate-cad",
             summary="Generate CAD from analysis result (Step 2)",
             description="Generate CAD code from PDF analysis result",
             response_model=PDFCADResponse)
async def generate_cad_from_analysis(
    request: PDFCADRequest,
    db: Session = Depends(get_db)
):
    """
    Generate CAD code from PDF analysis result (Step 2).

    Args:
        request: CAD generation request with analysis result
        db: Database session

    Returns:
        PDFCADResponse: Response with CAD generation result
    """
    logger.info(f"Received CAD generation request for session: {request.session_id}")

    try:
        processor = get_pdf_processor()

        if not processor.cad_agent:
            return PDFCADResponse(
                success=False,
                message="CAD agent not available",
                session_id=request.session_id,
                cad_generated=False
            )

        # Prepare input for CAD agent
        cad_input = request.analysis_result
        if request.additional_instructions:
            cad_input += f"\n\nAdditional instructions: {request.additional_instructions}"

        # Generate CAD using the chat tool (same as regular chat API)
        try:
            from ...schemas.sessions import ChatRequest as ApiChatRequest
            from ...crud.chat_processing import handle_chat_request
            from ...core.chatbot import text_to_cad_agent

            # Create ChatRequest for the tool chat
            chat_request = ApiChatRequest(
                message=cad_input,
                session_id=request.session_id,
                image_path="",
                part_file_name="pdf_generated_part",
                export_format="both",  # Generate both OBJ and STEP
                material_choice="STEEL",
                selected_feature_uuid="",
                is_edit_request=False
            )

            # Call the main chat tool
            result = handle_chat_request(
                db=db,
                chat_req=chat_request,
                agent=text_to_cad_agent,
                request_origin='pdf_api'
            )

            if result and hasattr(result, 'chat_response') and result.chat_response:
                return PDFCADResponse(
                    success=True,
                    message=result.chat_response,
                    session_id=request.session_id,
                    cad_generated=True,
                    code=getattr(result, 'code', None),
                    obj_export=getattr(result, 'obj_export', None),
                    step_export=getattr(result, 'step_export', None)
                )
            else:
                return PDFCADResponse(
                    success=False,
                    message="CAD generation failed",
                    session_id=request.session_id,
                    cad_generated=False
                )

        except Exception as cad_e:
            logger.error(f"Error in CAD generation: {cad_e}")
            return PDFCADResponse(
                success=False,
                message=f"CAD generation error: {str(cad_e)}",
                session_id=request.session_id,
                cad_generated=False
            )

    except Exception as e:
        logger.error(f"Error generating CAD from analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating CAD: {str(e)}")



@router.get("/history/{session_id}",
            summary="Get PDF chat history",
            description="Retrieve chat history for a PDF session",
            response_model=PDFChatHistoryResponse)
async def get_pdf_chat_history(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Retrieve chat history for a PDF session.

    Args:
        session_id: Session ID to get history for
        db: Database session

    Returns:
        PDFChatHistoryResponse: Chat history for the session
    """
    logger.info(f"Getting PDF chat history for session: {session_id}")

    try:
        from ...models.sessions import ChatHistory
        from ...schemas.pdf_chat import ChatMessage

        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append(ChatMessage(
                    timestamp=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="human",
                    content=entry.message
                ))

            # Add AI response if available
            if entry.response:
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.response
                ))
            elif entry.output:
                # Fallback to output if no response
                messages.append(ChatMessage(
                    timestamp=entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="ai",
                    content=entry.output
                ))

        return PDFChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting PDF chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")


@router.get("/formats",
            summary="Get supported PDF formats",
            description="Get information about supported PDF formats and limitations")
async def get_pdf_formats():
    """
    Get information about supported PDF formats and limitations.

    Returns:
        dict: Information about supported formats
    """
    return {
        "supported_formats": [".pdf"],
        "max_file_size": 20 * 1024 * 1024,  # 20MB
        "description": "PDF files are processed using OpenAI's file API for analysis and CAD generation"
    }
