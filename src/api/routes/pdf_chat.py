# -*- coding: utf-8 -*-
"""
PDF Chat API Routes

API endpoints for PDF chat functionality with CAD integration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session

from ...database.database import get_db
from ...utils.pdf_handler import PDFProcessor
from ...schemas.pdf_chat import (
    PDFProcessResponse
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/pdf-chat",
    tags=["pdf-chat"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize PDF processor with CAD agent
pdf_processor = None

def get_pdf_processor():
    """Get or create PDF processor instance with CAD agent."""
    global pdf_processor
    if pdf_processor is None:
        try:
            # Import the initialized agent from chatbot module
            from ...core.chatbot import text_to_cad_agent
            pdf_processor = PDFProcessor(cad_agent=text_to_cad_agent)
            logger.info("PDF processor initialized with CAD agent")
        except Exception as e:
            logger.error(f"Failed to initialize PDF processor with CAD agent: {e}")
            pdf_processor = PDFProcessor()
            logger.info("PDF processor initialized without CAD agent")
    return pdf_processor


@router.post("/process",
             summary="Upload PDF and generate CAD (Complete Flow)",
             description="Upload a PDF file, analyze it, and generate CAD code in one step",
             response_model=PDFProcessResponse)
async def process_pdf_complete(
    file: UploadFile = File(..., description="PDF file to upload"),
    message: str = Form(..., description="Initial message/question about the PDF"),
    session_id: Optional[str] = Form(None, description="Optional session ID for continuity"),
    db: Session = Depends(get_db)
):
    """
    Complete PDF processing: Upload → Analyze → Generate CAD.

    Args:
        file: Uploaded PDF file
        message: Initial message/question about the PDF
        session_id: Optional session ID for chat continuity
        db: Database session

    Returns:
        PDFProcessResponse: Response with analysis result and CAD generation
    """
    logger.info(f"Received PDF complete processing request: {file.filename}")

    # Validate file is a PDF
    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        processor = get_pdf_processor()

        # Generate session_id if not provided or invalid
        if not session_id or session_id == "string":
            final_session_id = processor._generate_session_id()
        else:
            final_session_id = session_id

        # Save uploaded file temporarily
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Step 1: Analyze PDF
            logger.info(f"Step 1: Analyzing PDF for session {final_session_id}")
            success, analysis_text, class_type, name_code = processor.analyze_pdf_only(
                db=db,
                session_id=final_session_id,
                file_path=temp_file_path,
                user_input=message
            )

            if not success:
                return PDFProcessResponse(
                    success=False,
                    message=f"PDF analysis failed: {analysis_text}",
                    session_id=final_session_id,
                    class_type=None,
                    name_code=None,
                    analysis_result=None,
                    cad_generated=False
                )

            logger.info(f"Step 1 completed: class_type={class_type}, name_code={name_code}")

            # Step 2: Generate CAD using tool chat
            logger.info(f"Step 2: Generating CAD for session {final_session_id}")

            if not processor.cad_agent:
                return PDFProcessResponse(
                    success=True,
                    message="PDF analyzed successfully but CAD agent not available",
                    session_id=final_session_id,
                    class_type=class_type,
                    name_code=name_code,
                    analysis_result=analysis_text,
                    cad_generated=False
                )

            # Prepare input for CAD generation
            cad_input = analysis_text
            if message and message.strip():
                cad_input += f"\n\nAdditional context: {message}"

            # Generate CAD using the chat tool
            try:
                from ...schemas.sessions import ChatRequest as ApiChatRequest
                from ...crud.chat_processing import handle_chat_request
                from ...core.chatbot import text_to_cad_agent

                # Create ChatRequest for the tool chat
                chat_request = ApiChatRequest(
                    message=cad_input,
                    session_id=final_session_id,
                    image_path="",
                    part_file_name="pdf_generated_part",
                    export_format="both",  # Generate both OBJ and STEP
                    material_choice="STEEL",
                    selected_feature_uuid="",
                    is_edit_request=False
                )

                # Call the main chat tool
                result = handle_chat_request(
                    db=db,
                    chat_req=chat_request,
                    agent=text_to_cad_agent,
                    request_origin='pdf_api'
                )

                if result and hasattr(result, 'chat_response') and result.chat_response:
                    logger.info(f"Step 2 completed: CAD generated successfully")
                    return PDFProcessResponse(
                        success=True,
                        message="PDF processed successfully and CAD generated",
                        session_id=final_session_id,
                        class_type=class_type,
                        name_code=name_code,
                        analysis_result=analysis_text,
                        cad_generated=True,
                        code=getattr(result, 'code', None),
                        obj_export=getattr(result, 'obj_export', None),
                        step_export=getattr(result, 'step_export', None)
                    )
                else:
                    logger.warning(f"Step 2 failed: CAD generation failed")
                    return PDFProcessResponse(
                        success=True,
                        message="PDF analyzed successfully but CAD generation failed",
                        session_id=final_session_id,
                        class_type=class_type,
                        name_code=name_code,
                        analysis_result=analysis_text,
                        cad_generated=False
                    )

            except Exception as cad_e:
                logger.error(f"Error in CAD generation: {cad_e}")
                return PDFProcessResponse(
                    success=True,
                    message=f"PDF analyzed successfully but CAD generation error: {str(cad_e)}",
                    session_id=final_session_id,
                    class_type=class_type,
                    name_code=name_code,
                    analysis_result=analysis_text,
                    cad_generated=False
                )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")



# Chat history is now handled by unified API: GET /api/chat-history/{session_id}


@router.get("/formats",
            summary="Get supported PDF formats",
            description="Get information about supported PDF formats and limitations")
async def get_pdf_formats():
    """
    Get information about supported PDF formats and limitations.

    Returns:
        dict: Information about supported formats
    """
    return {
        "supported_formats": [".pdf"],
        "max_file_size": 20 * 1024 * 1024,  # 20MB
        "description": "PDF files are processed using OpenAI's file API for analysis and CAD generation"
    }
