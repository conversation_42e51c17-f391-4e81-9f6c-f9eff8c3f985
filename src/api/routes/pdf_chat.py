# -*- coding: utf-8 -*-
"""
PDF Chat API Routes

API endpoints for PDF chat functionality with CAD integration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...utils.pdf_handler import PDFProcessor
from ...schemas.pdf_chat import (
    PDFChatRequest, 
    PDFChatResponse, 
    PDFUploadChatRequest,
    PDFChatHistoryResponse
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/pdf-chat",
    tags=["pdf-chat"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize PDF processor with CAD agent
pdf_processor = None

def get_pdf_processor():
    """Get or create PDF processor instance with CAD agent."""
    global pdf_processor
    if pdf_processor is None:
        try:
            from ...core.text_to_cad_agent import TextToCADAgent
            cad_agent = TextToCADAgent()
            pdf_processor = PDFProcessor(cad_agent=cad_agent)
            logger.info("PDF processor initialized with CAD agent")
        except Exception as e:
            logger.error(f"Failed to initialize PDF processor with CAD agent: {e}")
            pdf_processor = PDFProcessor()
            logger.info("PDF processor initialized without CAD agent")
    return pdf_processor


@router.post("/upload", 
             summary="Upload PDF and start chat",
             description="Upload a PDF file and start a chat session for analysis and CAD generation",
             response_model=PDFChatResponse)
async def upload_pdf_and_chat(
    file: UploadFile = File(..., description="PDF file to upload"),
    message: str = Form(..., description="Initial message/question about the PDF"),
    session_id: Optional[str] = Form(None, description="Optional session ID for continuity"),
    db: Session = Depends(get_db)
):
    """
    Upload a PDF file and start a chat session for analysis and CAD generation.
    
    Args:
        file: Uploaded PDF file
        message: Initial message/question about the PDF
        session_id: Optional session ID for chat continuity
        db: Database session
        
    Returns:
        PDFChatResponse: Response with analysis result and CAD generation status
    """
    logger.info(f"Received PDF upload and chat request: {file.filename}")
    
    # Validate file is a PDF
    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="File must be a PDF")
    
    try:
        processor = get_pdf_processor()
        
        # Process the uploaded file
        success, response_message, generated_session_id = await processor.process_uploaded_file(
            db=db, 
            uploaded_file=file, 
            user_input=message
        )
        
        # Use provided session_id or the generated one
        final_session_id = session_id or generated_session_id
        
        if success:
            # Check if CAD was generated by looking for export URLs in the response
            cad_generated = "CAD Generation:" in response_message
            
            # Extract analysis result if available
            analysis_result = None
            if "PDF Analysis:" in response_message:
                parts = response_message.split("PDF Analysis:")
                if len(parts) > 1:
                    analysis_part = parts[1].split("\n\nCAD Generation:")[0].strip()
                    analysis_result = analysis_part
            
            # TODO: Extract actual export URLs from the CAD agent response
            # For now, we'll construct them based on session_id if CAD was generated
            obj_export = None
            step_export = None
            if cad_generated:
                obj_export = f"/download/obj/{final_session_id}_latest.obj"
                step_export = f"/download/step/{final_session_id}_latest.step"
            
            return PDFChatResponse(
                success=True,
                message=response_message,
                session_id=final_session_id,
                analysis_result=analysis_result,
                cad_generated=cad_generated,
                obj_export=obj_export,
                step_export=step_export
            )
        else:
            return PDFChatResponse(
                success=False,
                message=response_message,
                session_id=final_session_id,
                analysis_result=None,
                cad_generated=False
            )
            
    except Exception as e:
        logger.error(f"Error processing PDF upload and chat: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")


@router.post("/chat/{session_id}",
             summary="Continue PDF chat conversation",
             description="Continue chatting about a previously uploaded PDF",
             response_model=PDFChatResponse)
async def continue_pdf_chat(
    session_id: str,
    request: PDFChatRequest,
    db: Session = Depends(get_db)
):
    """
    Continue a chat conversation about a previously uploaded PDF.
    
    Args:
        session_id: Session ID for the chat
        request: Chat request with message
        db: Database session
        
    Returns:
        PDFChatResponse: Response with chat result
    """
    logger.info(f"Continuing PDF chat for session: {session_id}")
    
    try:
        # TODO: Implement chat continuation logic
        # This would involve:
        # 1. Retrieving the session context from database
        # 2. Processing the new message with context
        # 3. Potentially generating new CAD if requested
        
        # For now, return a placeholder response
        return PDFChatResponse(
            success=True,
            message=f"Chat continuation for session {session_id} with message: {request.message}",
            session_id=session_id,
            analysis_result=None,
            cad_generated=False
        )
        
    except Exception as e:
        logger.error(f"Error continuing PDF chat: {e}")
        raise HTTPException(status_code=500, detail=f"Error continuing chat: {str(e)}")


@router.get("/history/{session_id}",
            summary="Get PDF chat history",
            description="Retrieve chat history for a PDF session",
            response_model=PDFChatHistoryResponse)
async def get_pdf_chat_history(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Retrieve chat history for a PDF session.
    
    Args:
        session_id: Session ID to get history for
        db: Database session
        
    Returns:
        PDFChatHistoryResponse: Chat history for the session
    """
    logger.info(f"Getting PDF chat history for session: {session_id}")
    
    try:
        # TODO: Implement history retrieval from database
        # This would query the chat_history table for the session
        
        # For now, return a placeholder response
        return PDFChatHistoryResponse(
            session_id=session_id,
            messages=[],
            total_messages=0
        )
        
    except Exception as e:
        logger.error(f"Error getting PDF chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")


@router.get("/formats",
            summary="Get supported PDF formats",
            description="Get information about supported PDF formats and limitations")
async def get_pdf_formats():
    """
    Get information about supported PDF formats and limitations.
    
    Returns:
        dict: Information about supported formats
    """
    return {
        "supported_formats": [".pdf"],
        "max_file_size": 20 * 1024 * 1024,  # 20MB
        "description": "PDF files are processed using OpenAI's file API for analysis and CAD generation"
    }
