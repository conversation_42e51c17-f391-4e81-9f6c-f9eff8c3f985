#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for new PDF and Image Chat APIs
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """Test importing all new modules"""
    print("🧪 Testing PDF and Image Chat API imports...")
    
    try:
        # Test schemas
        from src.schemas.pdf_chat import PDFChatRequest, PDFChatResponse
        print("✅ PDF Chat schemas imported successfully")
        
        from src.schemas.image_chat import ImageChatRequest, ImageChatResponse
        print("✅ Image Chat schemas imported successfully")
        
        # Test utils (these should already exist)
        from src.utils.pdf_handler import PDFProcessor
        print("✅ PDF Handler imported successfully")
        
        from src.utils.image_handler import ImageProcessor
        print("✅ Image Handler imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schema_creation():
    """Test creating schema instances"""
    print("\n🧪 Testing schema creation...")
    
    try:
        from src.schemas.pdf_chat import PDFChatRequest, PDFChatResponse
        from src.schemas.image_chat import ImageChatRequest, ImageChatResponse
        
        # Test PDF schemas
        pdf_request = PDFChatRequest(
            session_id="test_session_123",
            message="Test PDF message"
        )
        print("✅ PDF Chat Request created successfully")
        
        pdf_response = PDFChatResponse(
            success=True,
            message="Test response",
            session_id="test_session_123"
        )
        print("✅ PDF Chat Response created successfully")
        
        # Test Image schemas
        image_request = ImageChatRequest(
            session_id="test_session_456",
            message="Test image message"
        )
        print("✅ Image Chat Request created successfully")
        
        image_response = ImageChatResponse(
            success=True,
            message="Test response",
            session_id="test_session_456"
        )
        print("✅ Image Chat Response created successfully")
        
        print("\n🎉 All schema creation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Schema creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_structure():
    """Test API route structure"""
    print("\n🧪 Testing API route structure...")
    
    try:
        # Check if route files exist
        pdf_chat_path = os.path.join(project_root, "src", "api", "routes", "pdf_chat.py")
        image_chat_path = os.path.join(project_root, "src", "api", "routes", "image_chat.py")
        
        if os.path.exists(pdf_chat_path):
            print("✅ PDF Chat routes file exists")
        else:
            print("❌ PDF Chat routes file missing")
            return False
            
        if os.path.exists(image_chat_path):
            print("✅ Image Chat routes file exists")
        else:
            print("❌ Image Chat routes file missing")
            return False
        
        # Check documentation
        docs_path = os.path.join(project_root, "docs", "PDF_IMAGE_CHAT_API.md")
        if os.path.exists(docs_path):
            print("✅ API documentation exists")
        else:
            print("❌ API documentation missing")
            return False
        
        print("\n🎉 All API structure tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ API structure test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting PDF and Image Chat API Tests\n")
    
    tests = [
        test_imports,
        test_schema_creation,
        test_api_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print("-" * 50)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! PDF and Image Chat APIs are ready to use.")
        print("\n📚 Next steps:")
        print("1. Start the API server: python run.py")
        print("2. Visit http://localhost:8000/api-production/docs for API documentation")
        print("3. Test the endpoints with PDF and image files")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
