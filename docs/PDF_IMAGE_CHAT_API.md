# PDF và Image Chat API Documentation

## Tổng quan

Hệ thống đã được tích hợp thêm các tool chat chuyên dụng cho PDF và Image với khả năng phân tích và tạo CAD tự động. Các API này cho phép:

1. **PDF Chat**: Upload và chat với file PDF, phân tích nội dung, tạo CAD từ thông tin trong PDF
2. **Image Chat**: Upload và chat với file ảnh, phân tích hình ảnh, tạo CAD từ thông tin trong ảnh

## Cấu trúc API

### PDF Chat APIs

#### 1. Upload PDF và bắt đầu chat
```
POST /api-production/pdf-chat/upload
```

**Parameters:**
- `file`: PDF file (multipart/form-data)
- `message`: Tin nhắn/câu hỏi về PDF (form field)
- `session_id`: Session ID tùy chọn (form field)

**Response:**
```json
{
  "success": true,
  "message": "PDF analyzed successfully. Found perforated sheet with 5mm holes.",
  "session_id": "session_abc123_456789",
  "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
  "cad_generated": true,
  "obj_export": "/download/obj/session_abc123_456789_latest.obj",
  "step_export": "/download/step/session_abc123_456789_latest.step",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 2. Tiếp tục chat với PDF
```
POST /api-production/pdf-chat/chat/{session_id}
```

**Body:**
```json
{
  "session_id": "session_abc123_456789",
  "message": "What are the dimensions of the perforated sheet?"
}
```

#### 3. Lấy lịch sử chat PDF
```
GET /api-production/pdf-chat/history/{session_id}
```

#### 4. Thông tin format PDF hỗ trợ
```
GET /api-production/pdf-chat/formats
```

### Image Chat APIs

#### 1. Upload Image và bắt đầu chat
```
POST /api-production/image-chat/upload
```

**Parameters:**
- `file`: Image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
- `message`: Tin nhắn/câu hỏi về ảnh
- `session_id`: Session ID tùy chọn

**Response:**
```json
{
  "success": true,
  "message": "Image analyzed successfully. Found perforated sheet with circular holes.",
  "session_id": "session_abc123_456789",
  "analysis_result": "Class: perforated sheet, Name/code: PS-CIRCULAR-3MM",
  "cad_generated": true,
  "obj_export": "/download/obj/session_abc123_456789_latest.obj",
  "step_export": "/download/step/session_abc123_456789_latest.step",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 2. Tiếp tục chat với Image
```
POST /api-production/image-chat/chat/{session_id}
```

#### 3. Lấy lịch sử chat Image
```
GET /api-production/image-chat/history/{session_id}
```

#### 4. Thông tin format Image hỗ trợ
```
GET /api-production/image-chat/formats
```

**Response:**
```json
{
  "formats": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
  "max_file_size": 20971520
}
```

## Workflow hoạt động

### PDF Chat Workflow:
1. **Upload PDF**: User upload file PDF với câu hỏi ban đầu
2. **Phân tích PDF**: Hệ thống sử dụng OpenAI API để phân tích nội dung PDF
3. **Tạo CAD**: Nếu phát hiện thông tin về "Perforated sheet" hoặc "Tole", tự động tạo CAD
4. **Lưu database**: Lưu session, message, và kết quả vào database
5. **Trả về kết quả**: Response bao gồm phân tích và link download file CAD

### Image Chat Workflow:
1. **Upload Image**: User upload file ảnh với câu hỏi ban đầu
2. **Phân tích Image**: Hệ thống sử dụng OpenAI Vision API để phân tích hình ảnh
3. **Tạo CAD**: Nếu phát hiện thông tin về "Perforated sheet" hoặc "Tole", tự động tạo CAD
4. **Lưu database**: Lưu session, message, và kết quả vào database
5. **Trả về kết quả**: Response bao gồm phân tích và link download file CAD

## Tích hợp với hệ thống hiện tại

### Database Integration:
- Sử dụng chung bảng `chat_history` và `sessions`
- Tự động tạo session_id nếu không được cung cấp
- Lưu trữ thông tin file path và kết quả phân tích

### CAD Agent Integration:
- Tích hợp với `TextToCADAgent` hiện có
- Tự động trigger CAD generation khi phát hiện thông tin phù hợp
- Hỗ trợ cả OBJ và STEP file export

### File Management:
- Tự động xóa temporary files sau khi xử lý
- Hỗ trợ file size tối đa 20MB
- Validation format file nghiêm ngặt

## Cách sử dụng

### Ví dụ với cURL:

#### Upload PDF:
```bash
curl -X POST "http://localhost:8000/api-production/pdf-chat/upload" \
  -F "file=@perforated_sheet.pdf" \
  -F "message=Analyze this PDF and create a 3D model" \
  -F "session_id=my_session_123"
```

#### Upload Image:
```bash
curl -X POST "http://localhost:8000/api-production/image-chat/upload" \
  -F "file=@perforated_sheet.jpg" \
  -F "message=What type of perforated sheet is this?" \
  -F "session_id=my_session_456"
```

## Lưu ý quan trọng

1. **File Size Limit**: Tối đa 20MB cho cả PDF và Image
2. **Supported Formats**: 
   - PDF: .pdf
   - Image: .jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp
3. **Session Management**: Tự động tạo session_id nếu không cung cấp
4. **CAD Generation**: Chỉ trigger khi phát hiện "perforated sheet" hoặc "tole"
5. **Error Handling**: Comprehensive error handling và logging

## API Documentation

Truy cập Swagger UI để xem chi tiết:
- Development: `http://localhost:8000/api-test/docs`
- Production API: `http://localhost:8000/api-production/docs`
