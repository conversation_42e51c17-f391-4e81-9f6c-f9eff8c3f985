# PDF và Image Chat API Documentation

## Tổng quan

Hệ thống đã được tích hợp thêm các tool chat chuyên dụng cho PDF và Image với luồng xử lý 2 bước:

1. **Step 1**: Upload file → Phân tích → Trả về class/name
2. **Step 2**: G<PERSON>i kết quả phân tích → Chain kiểm tra và tạo CAD

**⚠️ Lưu ý**: PDF và Image Chat APIs được đặt trong **api-test** (không phải api-production)

## Luồng xử lý mới

### Workflow:
1. **Upload & Analyze** (Step 1): Upload PDF/Image → Phân tích bằng OpenAI → Trả về class, name/code
2. **Generate CAD** (Step 2): Gửi kết quả phân tích → Chain kiểm tra → Tạo CAD code

### PDF Chat APIs

#### 1. <PERSON>ân tích PDF (Step 1)
```
POST /api/pdf-chat/analyze
```

**Parameters:**
- `file`: PDF file (multipart/form-data)
- `message`: Tin nhắn/câu hỏi về PDF (form field)
- `session_id`: Session ID tùy chọn (form field)

**Response:**
```json
{
  "success": true,
  "message": "PDF analyzed successfully",
  "session_id": "session_abc123_456789",
  "class_type": "perforated sheet",
  "name_code": "PS-5MM-STEEL",
  "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 2. Tạo CAD từ kết quả phân tích (Step 2)
```
POST /api/pdf-chat/generate-cad
```

**Body:**
```json
{
  "session_id": "session_abc123_456789",
  "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
  "additional_instructions": "Make it 200x100x5mm with 5mm holes"
}
```

**Response:**
```json
{
  "success": true,
  "message": "CAD generated successfully from PDF analysis",
  "session_id": "session_abc123_456789",
  "cad_generated": true,
  "obj_export": "/download/obj/session_abc123_456789_latest.obj",
  "step_export": "/download/step/session_abc123_456789_latest.step",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 3. Lấy lịch sử chat PDF
```
GET /api/pdf-chat/history/{session_id}
```

#### 4. Thông tin format PDF hỗ trợ
```
GET /api/pdf-chat/formats
```

### Image Chat APIs

#### 1. Phân tích Image (Step 1)
```
POST /api/image-chat/analyze
```

**Parameters:**
- `file`: Image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
- `message`: Tin nhắn/câu hỏi về ảnh
- `session_id`: Session ID tùy chọn

**Response:**
```json
{
  "success": true,
  "message": "Image analyzed successfully",
  "session_id": "session_abc123_456789",
  "class_type": "perforated sheet",
  "name_code": "PS-CIRCULAR-3MM",
  "analysis_result": "Class: perforated sheet, Name/code: PS-CIRCULAR-3MM",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 2. Tạo CAD từ kết quả phân tích (Step 2)
```
POST /api/image-chat/generate-cad
```

**Body:**
```json
{
  "session_id": "session_abc123_456789",
  "analysis_result": "Class: perforated sheet, Name/code: PS-CIRCULAR-3MM",
  "additional_instructions": "Make it 300x200x3mm with 3mm circular holes"
}
```

**Response:**
```json
{
  "success": true,
  "message": "CAD generated successfully from image analysis",
  "session_id": "session_abc123_456789",
  "cad_generated": true,
  "obj_export": "/download/obj/session_abc123_456789_latest.obj",
  "step_export": "/download/step/session_abc123_456789_latest.step",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 3. Lấy lịch sử chat Image
```
GET /api/image-chat/history/{session_id}
```

#### 4. Thông tin format Image hỗ trợ
```
GET /api/image-chat/formats
```

**Response:**
```json
{
  "formats": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
  "max_file_size": 20971520
}
```

## Workflow hoạt động

### PDF Chat Workflow:
1. **Upload PDF**: User upload file PDF với câu hỏi ban đầu
2. **Phân tích PDF**: Hệ thống sử dụng OpenAI API để phân tích nội dung PDF
3. **Tạo CAD**: Nếu phát hiện thông tin về "Perforated sheet" hoặc "Tole", tự động tạo CAD
4. **Lưu database**: Lưu session, message, và kết quả vào database
5. **Trả về kết quả**: Response bao gồm phân tích và link download file CAD

### Image Chat Workflow:
1. **Upload Image**: User upload file ảnh với câu hỏi ban đầu
2. **Phân tích Image**: Hệ thống sử dụng OpenAI Vision API để phân tích hình ảnh
3. **Tạo CAD**: Nếu phát hiện thông tin về "Perforated sheet" hoặc "Tole", tự động tạo CAD
4. **Lưu database**: Lưu session, message, và kết quả vào database
5. **Trả về kết quả**: Response bao gồm phân tích và link download file CAD

## Tích hợp với hệ thống hiện tại

### Database Integration:
- Sử dụng chung bảng `chat_history` và `sessions`
- Tự động tạo session_id nếu không được cung cấp
- Lưu trữ thông tin file path và kết quả phân tích

### CAD Agent Integration:
- Tích hợp với `TextToCADAgent` hiện có
- Tự động trigger CAD generation khi phát hiện thông tin phù hợp
- Hỗ trợ cả OBJ và STEP file export

### File Management:
- Tự động xóa temporary files sau khi xử lý
- Hỗ trợ file size tối đa 20MB
- Validation format file nghiêm ngặt

## Cách sử dụng

### Ví dụ với cURL:

#### Step 1: Phân tích PDF
```bash
curl -X POST "http://localhost:8000/api/pdf-chat/analyze" \
  -F "file=@perforated_sheet.pdf" \
  -F "message=Analyze this PDF to identify class and name" \
  -F "session_id=my_session_123"
```

#### Step 2: Tạo CAD từ kết quả phân tích
```bash
curl -X POST "http://localhost:8000/api/pdf-chat/generate-cad" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "my_session_123",
    "analysis_result": "Class: perforated sheet, Name/code: PS-5MM-STEEL",
    "additional_instructions": "Make it 200x100x5mm with 5mm holes"
  }'
```

#### Step 1: Phân tích Image
```bash
curl -X POST "http://localhost:8000/api/image-chat/analyze" \
  -F "file=@perforated_sheet.jpg" \
  -F "message=Analyze this image to identify class and name" \
  -F "session_id=my_session_456"
```

#### Step 2: Tạo CAD từ kết quả phân tích Image
```bash
curl -X POST "http://localhost:8000/api/image-chat/generate-cad" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "my_session_456",
    "analysis_result": "Class: perforated sheet, Name/code: PS-CIRCULAR-3MM",
    "additional_instructions": "Make it 300x200x3mm with 3mm circular holes"
  }'
```

#### Lấy lịch sử chat:
```bash
curl -X GET "http://localhost:8000/api/pdf-chat/history/my_session_123"
curl -X GET "http://localhost:8000/api/image-chat/history/my_session_456"
```

## Lưu ý quan trọng

1. **File Size Limit**: Tối đa 20MB cho cả PDF và Image
2. **Supported Formats**:
   - PDF: .pdf
   - Image: .jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp
3. **Session Management**: Tự động tạo session_id nếu không cung cấp
4. **CAD Generation**: Chỉ trigger khi phát hiện "perforated sheet" hoặc "tole"
5. **Error Handling**: Comprehensive error handling và logging

## Lịch sử Chat Format

Lịch sử chat được trả về theo format mới với `role` và `content`:

```json
{
  "session_id": "aaad7848-8398-49a7-ae73-60d9f500c1ec",
  "messages": [
    {
      "timestamp": "2025-05-23 02:56:20.876646",
      "role": "human",
      "content": "boite 200x100x100"
    },
    {
      "timestamp": "2025-05-23 02:56:20.884813",
      "role": "ai",
      "content": "J'ai appliqué tout ce que vous avez demandé. N'hésitez pas à me demander de les modifier, ou d'ajouter autre chose comme des trous, des congés..."
    },
    {
      "timestamp": "2025-05-23 03:40:24.881896",
      "role": "human",
      "content": "faire un trou sur la face JMW"
    }
  ],
  "total_messages": 3
}
```

**Các trường:**
- `timestamp`: Thời gian tin nhắn (format: YYYY-MM-DD HH:MM:SS.ffffff)
- `role`: Vai trò - `"human"` cho user, `"ai"` cho chatbot
- `content`: Nội dung tin nhắn

## API Documentation

Truy cập Swagger UI để xem chi tiết:
- **API Test (PDF/Image Chat)**: `http://localhost:8000/api-test/docs`
- **API Production (CAD/Sessions)**: `http://localhost:8000/api-production/docs`
