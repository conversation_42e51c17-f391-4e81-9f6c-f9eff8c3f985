[{"id": 0, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeBox(length, width, height, [point])", "description": "Creates a box, origin point can be specified", "example": "box = Part.makeBox(10, 20, 5)", "source": "data/guide_en.txt", "text_content": "- `Part.makeBox(length, width, height, [point])` - Creates a box, origin point can be specified\n  *Example:* box = Part.makeBox(10, 20, 5)"}, {"id": 1, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeCylinder(radius, height, [point], [direction])", "description": "Creates a cylinder with radius and height", "example": "cylinder = <PERSON>.make<PERSON><PERSON><PERSON>(5, 30)", "source": "data/guide_en.txt", "text_content": "- `Part.makeCylinder(radius, height, [point], [direction])` - Creates a cylinder with radius and height\n  *Example:* cylinder = Part.makeCylinder(5, 30)"}, {"id": 2, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeSphere(radius, [point])", "description": "Creates a sphere with radius", "example": "sphere = Part.makeSphere(10)", "source": "data/guide_en.txt", "text_content": "- `Part.makeSphere(radius, [point])` - Creates a sphere with radius\n  *Example:* sphere = Part.makeSphere(10)"}, {"id": 3, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeCone(radius1, radius2, height, [point], [direction])", "description": "Creates a cone with 2 radii", "example": "* `cone = Part.makeCone(5, 0, 15)` # Pointed cone", "source": "data/guide_en.txt", "text_content": "- `Part.makeCone(radius1, radius2, height, [point], [direction])` - Creates a cone with 2 radii\n  *Example:* * `cone = Part.makeCone(5, 0, 15)` # Pointed cone"}, {"id": 4, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeTorus(radius1, radius2, [point], [direction], [angle1], [angle2])", "description": "Creates a torus", "example": "torus = <PERSON><PERSON>make<PERSON><PERSON>(10, 2)", "source": "data/guide_en.txt", "text_content": "- `Part.makeTorus(radius1, radius2, [point], [direction], [angle1], [angle2])` - Creates a torus\n  *Example:* torus = Part.makeTorus(10, 2)"}, {"id": 5, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeWedge(xmin, ymin, zmin, z2min, x2min, xmax, ymax, zmax, z2max, x2max)", "description": "Creates a wedge", "example": "wedge = Part.makeWedge(0,0,0, 5,5, 10,10,10, 15,15)", "source": "data/guide_en.txt", "text_content": "- `Part.makeWedge(xmin, ymin, zmin, z2min, x2min, xmax, ymax, zmax, z2max, x2max)` - Creates a wedge\n  *Example:* wedge = Part.makeWedge(0,0,0, 5,5, 10,10,10, 15,15)"}, {"id": 6, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makePrism(face, direction)", "description": "Creates a prism from a face and direction vector", "example": "face = Part.makePlane(10,10); prism = Part.makePrism(face, App.Vector(0,0,5))", "source": "data/guide_en.txt", "text_content": "- `Part.makePrism(face, direction)` - Creates a prism from a face and direction vector\n  *Example:* face = Part.makePlane(10,10); prism = Part.makePrism(face, App.Vector(0,0,5))"}, {"id": 7, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Basic Objects", "command_signature": "Part.makeHelix(pitch, height, radius, [angle])", "description": "Creates a helix", "example": "helix = Part.makeHelix(pitch=2, height=10, radius=3)", "source": "data/guide_en.txt", "text_content": "- `Part.makeHelix(pitch, height, radius, [angle])` - Creates a helix\n  *Example:* helix = Part.makeHelix(pitch=2, height=10, radius=3)"}, {"id": 8, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Boolean Operations", "command_signature": "Part.cut(shape1, shape2)", "description": "Cuts shape2 from shape1", "example": "box1 = Part.makeBox(10,10,10); box2 = Part.makeBox(5,5,5, App.Vector(2,2,2)); cut_obj = Part.cut(box1, box2)", "source": "data/guide_en.txt", "text_content": "- `Part.cut(shape1, shape2)` - Cuts shape2 from shape1\n  *Example:* box1 = Part.makeBox(10,10,10); box2 = Part.makeBox(5,5,5, App.Vector(2,2,2)); cut_obj = Part.cut(box1, box2)"}, {"id": 9, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Boolean Operations", "command_signature": "Part.fuse(shape1, shape2)", "description": "Combines shape1 and shape2", "example": "cyl1 = Part.makeCylinder(5,10); cyl2 = Part.makeCylinder(3,15); fused_obj = Part.fuse(cyl1, cyl2)", "source": "data/guide_en.txt", "text_content": "- `Part.fuse(shape1, shape2)` - Combines shape1 and shape2\n  *Example:* cyl1 = Part.makeCylinder(5,10); cyl2 = Part.makeCylinder(3,15); fused_obj = Part.fuse(cyl1, cyl2)"}, {"id": 10, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Boolean Operations", "command_signature": "Part.common(shape1, shape2)", "description": "Creates the intersection of shape1 and shape2", "example": "sph1 = Part.makeSphere(10); sph2 = Part.makeSphere(8, App.Vector(5,0,0)); common_obj = Part.common(sph1, sph2)", "source": "data/guide_en.txt", "text_content": "- `Part.common(shape1, shape2)` - Creates the intersection of shape1 and shape2\n  *Example:* sph1 = Part.makeSphere(10); sph2 = Part.makeSphere(8, App.Vector(5,0,0)); common_obj = Part.common(sph1, sph2)"}, {"id": 11, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Boolean Operations", "command_signature": "Part.section(shape1, shape2)", "description": "Creates a cross-section between shape1 and shape2", "example": "box = Part.makeBox(20,20,5); plane = Part.makePlane(30,30); section_obj = Part.section(box, plane)", "source": "data/guide_en.txt", "text_content": "- `Part.section(shape1, shape2)` - Creates a cross-section between shape1 and shape2\n  *Example:* box = Part.makeBox(20,20,5); plane = Part.makePlane(30,30); section_obj = Part.section(box, plane)"}, {"id": 12, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeLoft(listOfWires, [solid], [ruled])", "description": "Creates a loft surface from a list of wires", "example": "w1 = Part.makeWire([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(10,10,0), App.Vector(0,10,0)], True); w2 = Part.makeWire([App.Vector(2,2,5), App.Vector(8,2,5), App.Vector(8,8,5), App.Vector(2,8,5)], True); loft = Part.makeLoft([w1, w2], True)", "source": "data/guide_en.txt", "text_content": "- `Part.makeLoft(listOfWires, [solid], [ruled])` - Creates a loft surface from a list of wires\n  *Example:* w1 = Part.makeWire([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(10,10,0), App.Vector(0,10,0)], True); w2 = Part.makeWire([App.Vector(2,2,5), App.Vector(8,2,5), App.Vector(8,8,5), App.Vector(2,8,5)], True); loft = Part.makeLoft([w1, w2], True)"}, {"id": 13, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeSweep(spine, profile, [transition])", "description": "Creates a sweep object along a path", "example": "spine = Part.makeLine((0,0,0), (0,0,20)); profile = Part.makeCircle(5); sweep = Part.makeSweep(spine, profile)", "source": "data/guide_en.txt", "text_content": "- `Part.makeSweep(spine, profile, [transition])` - Creates a sweep object along a path\n  *Example:* spine = Part.makeLine((0,0,0), (0,0,20)); profile = Part.makeCircle(5); sweep = Part.makeSweep(spine, profile)"}, {"id": 14, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeExtrusion(face, vector)", "description": "Extrudes a face along a vector", "example": "face = Part.makePlane(10,10); extrusion = Part.makeExtrusion(face, App.Vector(0,0,15))", "source": "data/guide_en.txt", "text_content": "- `Part.makeExtrusion(face, vector)` - Extrudes a face along a vector\n  *Example:* face = Part.makePlane(10,10); extrusion = Part.makeExtrusion(face, App.Vector(0,0,15))"}, {"id": 15, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeRevolution(profile, [placement], [angle], [solid])", "description": "Creates a revolved object", "example": "profile = Part.makeCircle(10, App.Vector(15,0,0)); revolution = Part.makeRevolution(profile)", "source": "data/guide_en.txt", "text_content": "- `Part.makeRevolution(profile, [placement], [angle], [solid])` - Creates a revolved object\n  *Example:* profile = Part.makeCircle(10, App.Vector(15,0,0)); revolution = Part.makeRevolution(profile)"}, {"id": 16, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeShell(listOfFaces)", "description": "Creates a shell from a list of faces (often for removing faces)", "example": "box = Part.makeBox(10,10,10); faces_to_remove = [box.Faces[0]]; shell = Part.makeShell(faces_to_remove)", "source": "data/guide_en.txt", "text_content": "- `Part.makeShell(listOfFaces)` - Creates a shell from a list of faces (often for removing faces)\n  *Example:* box = Part.makeBox(10,10,10); faces_to_remove = [box.Faces[0]]; shell = Part.makeShell(faces_to_remove)"}, {"id": 17, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.make<PERSON>olid(shell)", "description": "Creates a solid from a shell", "example": "# Assuming 'shell' is a valid Part.Shell object; solid = Part.makeSolid(shell)", "source": "data/guide_en.txt", "text_content": "- `Part.makeSolid(shell)` - Creates a solid from a shell\n  *Example:* # Assuming 'shell' is a valid Part.Shell object; solid = Part.makeSolid(shell)"}, {"id": 18, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeCompound(listOfShapes)", "description": "Creates a compound from a list of shapes", "example": "box = Part.makeBox(5,5,5); sphere = Part.makeSphere(3); compound = Part.makeCompound([box, sphere])", "source": "data/guide_en.txt", "text_content": "- `Part.makeCompound(listOfShapes)` - Creates a compound from a list of shapes\n  *Example:* box = Part.makeBox(5,5,5); sphere = Part.makeSphere(3); compound = Part.makeCompound([box, sphere])"}, {"id": 19, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeFilledFace(listOfWires)", "description": "Creates a face from a list of wires", "example": "wire = Part.makeWire([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(5,5,0)], True); face = Part.makeFilledFace([wire])", "source": "data/guide_en.txt", "text_content": "- `Part.makeFilledFace(listOfWires)` - Creates a face from a list of wires\n  *Example:* wire = Part.makeWire([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(5,5,0)], True); face = Part.makeFilledFace([wire])"}, {"id": 20, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeOffset(shape, offset, [tolerance])", "description": "Creates an offset object", "example": "box = Part.makeBox(10,10,10); offset_box = Part.makeOffset(box, 2)", "source": "data/guide_en.txt", "text_content": "- `Part.makeOffset(shape, offset, [tolerance])` - Creates an offset object\n  *Example:* box = Part.makeBox(10,10,10); offset_box = Part.makeOffset(box, 2)"}, {"id": 21, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Complex Shape Creation Operations", "command_signature": "Part.makeThickness(shape, thickness, [tolerance])", "description": "Creates a thick object", "example": "* `sphere = Part.makeSphere(10); thick_sphere = Part.makeThickness(sphere, -1)` # Negative for inward thickness", "source": "data/guide_en.txt", "text_content": "- `Part.makeThickness(shape, thickness, [tolerance])` - Creates a thick object\n  *Example:* * `sphere = Part.makeSphere(10); thick_sphere = Part.makeThickness(sphere, -1)` # Negative for inward thickness"}, {"id": 22, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Lines and Curves", "command_signature": "Part.makeCircle(radius, [center], [direction], [angle1], [angle2])", "description": "Creates a circle", "example": "circle = Part.makeCircle(10)", "source": "data/guide_en.txt", "text_content": "- `Part.makeCircle(radius, [center], [direction], [angle1], [angle2])` - Creates a circle\n  *Example:* circle = Part.makeCircle(10)"}, {"id": 23, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Lines and Curves", "command_signature": "Part.makeEllipse(major_radius, minor_radius, [center], [direction])", "description": "Creates an ellipse", "example": "ellipse = <PERSON>.makeEllipse(10, 5)", "source": "data/guide_en.txt", "text_content": "- `Part.makeEllipse(major_radius, minor_radius, [center], [direction])` - Creates an ellipse\n  *Example:* ellipse = Part.makeEllipse(10, 5)"}, {"id": 24, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Lines and Curves", "command_signature": "Part.makePolygon(listOfPoints)", "description": "Creates a polygon from a list of points", "example": "poly = Part.makePolygon([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(5,10,0)])", "source": "data/guide_en.txt", "text_content": "- `Part.makePolygon(listOfPoints)` - Creates a polygon from a list of points\n  *Example:* poly = Part.makePolygon([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(5,10,0)])"}, {"id": 25, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Lines and Curves", "command_signature": "Part.makeSpline(listOfPoints, [periodic])", "description": "Creates a spline", "example": "spline = Part.makeSpline([App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)])", "source": "data/guide_en.txt", "text_content": "- `Part.makeSpline(listOfPoints, [periodic])` - Creates a spline\n  *Example:* spline = Part.makeSpline([App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)])"}, {"id": 26, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Lines and Curves", "command_signature": "Part.makeBSpline(poles, [knots], [mults], [periodic], [degree], [weights])", "description": "Creates a BSpline", "example": "poles = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bspline = Part.makeBSpline(poles)", "source": "data/guide_en.txt", "text_content": "- `Part.makeBSpline(poles, [knots], [mults], [periodic], [degree], [weights])` - Creates a BSpline\n  *Example:* poles = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bspline = Part.makeBSpline(poles)"}, {"id": 27, "workbench": "1. Part Workbench (Core for 3D Modeling)", "category": "Lines and Curves", "command_signature": "Part.makeBezierCurve(poles)", "description": "Creates a <PERSON>zier curve", "example": "poles = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bezier = Part.makeBezierCurve(poles)", "source": "data/guide_en.txt", "text_content": "- `Part.makeBezierCurve(poles)` - Creates a Bezier curve\n  *Example:* poles = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bezier = Part.makeBezierCurve(poles)"}, {"id": 28, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Sketch-based Features", "command_signature": "PartDesign.makePad(sketch, length, [midplane], [reversed], [mode])", "description": "Creates a pad feature (extrusion)", "example": "# Assuming 'sketch' is a valid Sketcher.SketchObject; pad = PartDesign.makePad(sketch, 10)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makePad(sketch, length, [midplane], [reversed], [mode])` - Creates a pad feature (extrusion)\n  *Example:* # Assuming 'sketch' is a valid Sketcher.SketchObject; pad = PartDesign.makePad(sketch, 10)"}, {"id": 29, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Sketch-based Features", "command_signature": "PartDesign.makePocket(sketch, length, [midplane], [reversed], [mode])", "description": "Creates a pocket feature (cut extrusion)", "example": "# Assuming 'sketch' is a valid Sketcher.SketchObject; pocket = PartDesign.makePocket(sketch, 5)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makePocket(sketch, length, [midplane], [reversed], [mode])` - Creates a pocket feature (cut extrusion)\n  *Example:* # Assuming 'sketch' is a valid Sketcher.SketchObject; pocket = PartDesign.makePocket(sketch, 5)"}, {"id": 30, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Sketch-based Features", "command_signature": "PartDesign.makeRevolution(sketch, angle, [midplane], [reversed])", "description": "Creates a revolution feature", "example": "# Assuming 'sketch' is a valid Sketcher.SketchObject; revolution = PartDesign.makeRevolution(sketch, 360)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeRevolution(sketch, angle, [midplane], [reversed])` - Creates a revolution feature\n  *Example:* # Assuming 'sketch' is a valid Sketcher.SketchObject; revolution = PartDesign.makeRevolution(sketch, 360)"}, {"id": 31, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Sketch-based Features", "command_signature": "PartDesign.makeGroove(sketch, angle, [midplane], [reversed])", "description": "Creates a groove feature (revolved cut)", "example": "# Assuming 'sketch' is a valid Sketcher.SketchObject; groove = PartDesign.makeGroove(sketch, 360)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeGroove(sketch, angle, [midplane], [reversed])` - Creates a groove feature (revolved cut)\n  *Example:* # Assuming 'sketch' is a valid Sketcher.SketchObject; groove = PartDesign.makeGroove(sketch, 360)"}, {"id": 32, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Transitional Features", "command_signature": "PartDesign.makeFillet(body, edges, radius)", "description": "Creates an edge fillet", "example": "# Assuming 'body' is a PartDesign.Body and 'edges' is a list of edges; fillet = PartDesign.makeFillet(body, edges, 2)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeFillet(body, edges, radius)` - Creates an edge fillet\n  *Example:* # Assuming 'body' is a PartDesign.Body and 'edges' is a list of edges; fillet = PartDesign.makeFillet(body, edges, 2)"}, {"id": 33, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Transitional Features", "command_signature": "PartDesign.makeChamfer(body, edges, distance)", "description": "Creates an edge chamfer", "example": "# Assuming 'body' is a PartDesign.Body and 'edges' is a list of edges; chamfer = PartDesign.makeChamfer(body, edges, 1)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeChamfer(body, edges, distance)` - Creates an edge chamfer\n  *Example:* # Assuming 'body' is a PartDesign.Body and 'edges' is a list of edges; chamfer = PartDesign.makeChamfer(body, edges, 1)"}, {"id": 34, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Transitional Features", "command_signature": "PartDesign.makeDraft(body, faces, angle)", "description": "Creates a draft feature", "example": "* `# Assuming 'body' is a PartDesign.Body and 'faces' is a list of faces; draft = PartDesign.makeDraft(body, faces, 5)` # 5 degree draft angle", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeDraft(body, faces, angle)` - Creates a draft feature\n  *Example:* * `# Assuming 'body' is a PartDesign.Body and 'faces' is a list of faces; draft = PartDesign.makeDraft(body, faces, 5)` # 5 degree draft angle"}, {"id": 35, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Transitional Features", "command_signature": "PartDesign.makeThickness(body, faces, thickness, [mode])", "description": "Creates a thickness feature", "example": "# Assuming 'body' is a PartDesign.Body and 'faces' is a list of faces; thickness = PartDesign.makeThickness(body, faces, 1)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeThickness(body, faces, thickness, [mode])` - Creates a thickness feature\n  *Example:* # Assuming 'body' is a PartDesign.Body and 'faces' is a list of faces; thickness = PartDesign.makeThickness(body, faces, 1)"}, {"id": 36, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Pattern Features", "command_signature": "PartDesign.makeLinearPattern(feature, direction, length, occurrences)", "description": "Creates a linear pattern", "example": "* `# Assuming 'feature' is a PartDesign feature; pattern = PartDesign.makeLinearPattern(feature, App.Vector(1,0,0), 50, 5)` # 5 occurrences over 50mm along X axis", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeLinearPattern(feature, direction, length, occurrences)` - Creates a linear pattern\n  *Example:* * `# Assuming 'feature' is a PartDesign feature; pattern = PartDesign.makeLinearPattern(feature, App.Vector(1,0,0), 50, 5)` # 5 occurrences over 50mm along X axis"}, {"id": 37, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Pattern Features", "command_signature": "PartDesign.makePolarPattern(feature, axis, angle, occurrences)", "description": "Creates a polar pattern", "example": "* `# Assuming 'feature' is a PartDesign feature and 'axis' is a reference axis; pattern = PartDesign.makePolarPattern(feature, axis, 360, 6)` # 6 occurrences over 360 degrees", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makePolarPattern(feature, axis, angle, occurrences)` - Creates a polar pattern\n  *Example:* * `# Assuming 'feature' is a PartDesign feature and 'axis' is a reference axis; pattern = PartDesign.makePolarPattern(feature, axis, 360, 6)` # 6 occurrences over 360 degrees"}, {"id": 38, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Pattern Features", "command_signature": "PartDesign.makeMultiTransform(feature)", "description": "Creates a multi-transform feature (combines multiple transformations)", "example": "* `# Assuming 'feature' is a PartDesign feature; multi = PartDesign.makeMultiTransform(feature)` # Sub-transformations need to be defined", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeMultiTransform(feature)` - Creates a multi-transform feature (combines multiple transformations)\n  *Example:* * `# Assuming 'feature' is a PartDesign feature; multi = PartDesign.makeMultiTransform(feature)` # Sub-transformations need to be defined"}, {"id": 39, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Pattern Features", "command_signature": "PartDesign.makeScaled(feature, factor, [center])", "description": "Creates a scaled feature", "example": "* `# Assuming 'feature' is a PartDesign feature; scaled = PartDesign.makeScaled(feature, 1.5)` # Scale factor 1.5", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeScaled(feature, factor, [center])` - Creates a scaled feature\n  *Example:* * `# Assuming 'feature' is a PartDesign feature; scaled = PartDesign.makeScaled(feature, 1.5)` # Scale factor 1.5"}, {"id": 40, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Pattern Features", "command_signature": "PartDesign.makeMirrored(feature, plane)", "description": "Creates a mirrored feature", "example": "# Assuming 'feature' is a PartDesign feature and 'plane' is a reference plane; mirrored = PartDesign.makeMirrored(feature, plane)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeMirrored(feature, plane)` - Creates a mirrored feature\n  *Example:* # Assuming 'feature' is a PartDesign feature and 'plane' is a reference plane; mirrored = PartDesign.makeMirrored(feature, plane)"}, {"id": 41, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Advanced Features", "command_signature": "PartDesign.makeLoft(profiles, [solid], [ruled])", "description": "Creates a loft feature", "example": "# Assuming 'profiles' is a list of Sketcher.SketchObject; loft = PartDesign.makeLoft(profiles, True)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeLoft(profiles, [solid], [ruled])` - Creates a loft feature\n  *Example:* # Assuming 'profiles' is a list of Sketcher.SketchObject; loft = PartDesign.makeLoft(profiles, True)"}, {"id": 42, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Advanced Features", "command_signature": "PartDesign.makePipe(spine, profile)", "description": "Creates a pipe along a path", "example": "# Assuming 'spine' and 'profile' are Sketcher.SketchObject; pipe = PartDesign.makePipe(spine, profile)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makePipe(spine, profile)` - Creates a pipe along a path\n  *Example:* # Assuming 'spine' and 'profile' are Sketcher.SketchObject; pipe = PartDesign.makePipe(spine, profile)"}, {"id": 43, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Advanced Features", "command_signature": "PartDesign.makeAdditive(body, tool)", "description": "Creates an additive feature (similar to Fuse)", "example": "# Assuming 'body' and 'tool' are PartDesign features/bodies; additive = PartDesign.makeAdditive(body, tool)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeAdditive(body, tool)` - Creates an additive feature (similar to Fuse)\n  *Example:* # Assuming 'body' and 'tool' are PartDesign features/bodies; additive = PartDesign.makeAdditive(body, tool)"}, {"id": 44, "workbench": "2. PartDesign Workbench (Feature-based Modeling)", "category": "Advanced Features", "command_signature": "PartDesign.makeSubtractive(body, tool)", "description": "Creates a subtractive feature (similar to Cut)", "example": "# Assuming 'body' and 'tool' are PartDesign features/bodies; subtractive = PartDesign.makeSubtractive(body, tool)", "source": "data/guide_en.txt", "text_content": "- `PartDesign.makeSubtractive(body, tool)` - Creates a subtractive feature (similar to Cut)\n  *Example:* # Assuming 'body' and 'tool' are PartDesign features/bodies; subtractive = PartDesign.makeSubtractive(body, tool)"}, {"id": 45, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makePoint(X, Y, Z)", "description": "Creates a point", "example": "pt = Draft.makePoint(10, 5, 2)", "source": "data/guide_en.txt", "text_content": "- `Draft.makePoint(X, Y, Z)` - Creates a point\n  *Example:* pt = Draft.makePoint(10, 5, 2)"}, {"id": 46, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeLine(point1, point2)", "description": "Creates a line", "example": "p1 = App.Vector(0,0,0); p2 = App.Vector(10,10,10); line = Draft.makeLine(p1, p2)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeLine(point1, point2)` - Creates a line\n  *Example:* p1 = App.Vector(0,0,0); p2 = App.Vector(10,10,10); line = Draft.makeLine(p1, p2)"}, {"id": 47, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeWire([points], [closed])", "description": "Creates a wire from a list of points", "example": "points = [App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(10,10,0)]; wire = Draft.makeWire(points)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeWire([points], [closed])` - Creates a wire from a list of points\n  *Example:* points = [App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(10,10,0)]; wire = Draft.makeWire(points)"}, {"id": 48, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeBSpline([points], [closed])", "description": "Creates a B-spline", "example": "points = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bspline = Draft.makeBSpline(points)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeBSpline([points], [closed])` - Creates a B-spline\n  *Example:* points = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bspline = Draft.makeBSpline(points)"}, {"id": 49, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeBezCurve([points], [closed])", "description": "Creates a <PERSON>zier curve", "example": "points = <PERSON>([App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bezcurve = Draft.makeBezCurve(points)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeBezCurve([points], [closed])` - Creates a Bezier curve\n  *Example:* points = <PERSON>([App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bezcurve = Draft.makeBezCurve(points)"}, {"id": 50, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeCircle(radius, [placement])", "description": "Creates a circle", "example": "circle = Draft.makeCircle(5)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeCircle(radius, [placement])` - Creates a circle\n  *Example:* circle = Draft.makeCircle(5)"}, {"id": 51, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeEllipse(major_radius, minor_radius)", "description": "Creates an ellipse", "example": "ellipse = Draft.makeE<PERSON>pse(10, 5)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeEllipse(major_radius, minor_radius)` - Creates an ellipse\n  *Example:* ellipse = Draft.makeEllipse(10, 5)"}, {"id": 52, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeRectangle(length, width, [placement])", "description": "Creates a rectangle", "example": "rect = Draft.makeR<PERSON>tangle(20, 10)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeRectangle(length, width, [placement])` - Creates a rectangle\n  *Example:* rect = Draft.makeRectangle(20, 10)"}, {"id": 53, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makePolygon(numberofedges, radius)", "description": "Creates a regular polygon", "example": "* `poly = Draft.makePolygon(6, 10)` # Hexagon with circumscribing circle radius 10", "source": "data/guide_en.txt", "text_content": "- `Draft.makePolygon(numberofedges, radius)` - Creates a regular polygon\n  *Example:* * `poly = Draft.makePolygon(6, 10)` # Hexagon with circumscribing circle radius 10"}, {"id": 54, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeText(string, [placement])", "description": "Creates text", "example": "text = Draft.makeText(\"Hello FreeCAD\")", "source": "data/guide_en.txt", "text_content": "- `Draft.makeText(string, [placement])` - Creates text\n  *Example:* text = Draft.makeText(\"Hello FreeCAD\")"}, {"id": 55, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "Basic Objects (can be used in 3D space)", "command_signature": "Draft.makeShapeString(string, fontfile, size)", "description": "Creates a shape string (geometry from text)", "example": "# Assuming font file exists; shape_string = Draft.makeShapeString(\"CAD\", \"C:/Windows/Fonts/arial.ttf\", 10)", "source": "data/guide_en.txt", "text_content": "- `Draft.makeShapeString(string, fontfile, size)` - Creates a shape string (geometry from text)\n  *Example:* # Assuming font file exists; shape_string = Draft.makeShapeString(\"CAD\", \"C:/Windows/Fonts/arial.ttf\", 10)"}, {"id": 56, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Draft.extrude(object, direction)", "description": "Extrudes an object along a direction", "example": "box = Part.makeBox(5,5,5); extruded_box = Draft.extrude(box, App.Vector(0,0,10))", "source": "data/guide_en.txt", "text_content": "- `Draft.extrude(object, direction)` - Extrudes an object along a direction\n  *Example:* box = Part.makeBox(5,5,5); extruded_box = Draft.extrude(box, App.Vector(0,0,10))"}, {"id": 57, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Draft.move(objectslist, vector, [copy])", "description": "Moves objects in 3D space", "example": "obj = Part.makeSphere(5); Draft.move(obj, App.Vector(10, 0, 0))", "source": "data/guide_en.txt", "text_content": "- `Draft.move(objectslist, vector, [copy])` - Moves objects in 3D space\n  *Example:* obj = Part.makeSphere(5); Draft.move(obj, App.Vector(10, 0, 0))"}, {"id": 58, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Draft.rotate(objectslist, angle, center, axis, [copy])", "description": "Rotates objects", "example": "* `obj = Part.makeBox(10,10,10); Draft.rotate(obj, 45, App.Vector(0,0,0), App.Vector(0,0,1))` # Rotate 45 degrees around Z axis", "source": "data/guide_en.txt", "text_content": "- `Draft.rotate(objectslist, angle, center, axis, [copy])` - Rotates objects\n  *Example:* * `obj = Part.makeBox(10,10,10); Draft.rotate(obj, 45, App.Vector(0,0,0), App.Vector(0,0,1))` # Rotate 45 degrees around Z axis"}, {"id": 59, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Draft.scale(objectslist, scale, center, [copy])", "description": "Scales objects", "example": "* `obj = <PERSON>.make<PERSON><PERSON>inder(5,10); Draft.scale(obj, App.Vector(2,2,1))` # Scale X, Y by 2, Z remains the same", "source": "data/guide_en.txt", "text_content": "- `Draft.scale(objectslist, scale, center, [copy])` - Scales objects\n  *Example:* * `obj = Part.makeCylinder(5,10); Draft.scale(obj, App.Vector(2,2,1))` # Scale X, Y by 2, Z remains the same"}, {"id": 60, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Draft.offset(object, offset, [copy])", "description": "Creates an offset of an object", "example": "line = Draft.makeLine(App.Vector(0,0,0), App.Vector(10,0,0)); offset_line = Draft.offset(line, 2)", "source": "data/guide_en.txt", "text_content": "- `Draft.offset(object, offset, [copy])` - Creates an offset of an object\n  *Example:* line = Draft.makeLine(App.Vector(0,0,0), App.Vector(10,0,0)); offset_line = Draft.offset(line, 2)"}, {"id": 61, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Curve.makeBlendCurve([points], [degree])", "description": "Creates a blend curve (smooth connection)", "example": "# Assuming points list; blend = Curve.makeBlendCurve(points)", "source": "data/guide_en.txt", "text_content": "- `Curve.makeBlendCurve([points], [degree])` - Creates a blend curve (smooth connection)\n  *Example:* # Assuming points list; blend = Curve.makeBlendCurve(points)"}, {"id": 62, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Curve.makeCurveParametric(fx, fy, fz, bounds)", "description": "Creates a parametric curve", "example": "* `# Define parametric functions fx, fy, fz; curve = Curve.makeCurveParametric(fx, fy, fz, [0, 1])` # Parameter t from 0 to 1", "source": "data/guide_en.txt", "text_content": "- `Curve.makeCurveParametric(fx, fy, fz, bounds)` - Creates a parametric curve\n  *Example:* * `# Define parametric functions fx, fy, fz; curve = Curve.makeCurveParametric(fx, fy, fz, [0, 1])` # Parameter t from 0 to 1"}, {"id": 63, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Curve.makeBSplineApproximation(points, [degree])", "description": "Creates a B-spline approximation from points", "example": "# Assuming points list; approx = Curve.makeBSplineApproximation(points)", "source": "data/guide_en.txt", "text_content": "- `Curve.makeBSplineApproximation(points, [degree])` - Creates a B-spline approximation from points\n  *Example:* # Assuming points list; approx = Curve.makeBSplineApproximation(points)"}, {"id": 64, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Curve.makeCurveOnSurface(curve, surface)", "description": "Creates a curve on a surface", "example": "# Assuming curve and surface objects; curve_on_surf = Curve.makeCurveOnSurface(curve, surface)", "source": "data/guide_en.txt", "text_content": "- `Curve.makeCurveOnSurface(curve, surface)` - Creates a curve on a surface\n  *Example:* # Assuming curve and surface objects; curve_on_surf = Curve.makeCurveOnSurface(curve, surface)"}, {"id": 65, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Curve.make<PERSON><PERSON><PERSON><PERSON>(spine, profiles)", "description": "Creates a pipe shell along a spine and profiles", "example": "# Assuming spine wire and profiles list; pipeshell = Curve.makePipeshell(spine, profiles)", "source": "data/guide_en.txt", "text_content": "- `Curve.makeP<PERSON>eshell(spine, profiles)` - Creates a pipe shell along a spine and profiles\n  *Example:* # Assuming spine wire and profiles list; pipeshell = Curve.makePipeshell(spine, profiles)"}, {"id": 66, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Curve.makeSweep(path, profile, [solid])", "description": "Creates a sweep along a profile (similar to Part.makeSweep)", "example": "# Assuming path wire and profile face; sweep = Curve.makeSweep(path, profile)", "source": "data/guide_en.txt", "text_content": "- `Curve.makeSweep(path, profile, [solid])` - Creates a sweep along a profile (similar to Part.makeSweep)\n  *Example:* # Assuming path wire and profile face; sweep = Curve.makeSweep(path, profile)"}, {"id": 67, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createBSplineSurface(poles, [uknots], [vknots], [umults], [vmults], [udegree], [vdegree])", "description": "Creates a B-spline surface", "example": "# Assuming poles grid, knots, mults, degrees; bsurf = Surface.createBSplineSurface(poles)", "source": "data/guide_en.txt", "text_content": "- `Surface.createBSplineSurface(poles, [uknots], [vknots], [umults], [vmults], [udegree], [vdegree])` - Creates a B-spline surface\n  *Example:* # Assuming poles grid, knots, mults, degrees; bsurf = Surface.createBSplineSurface(poles)"}, {"id": 68, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createBezierSurface(poles)", "description": "Creates a Bezier surface", "example": "# Assuming poles grid; bezsurf = Surface.createBezierSurface(poles)", "source": "data/guide_en.txt", "text_content": "- `Surface.createBezierSurface(poles)` - Creates a Bezier surface\n  *Example:* # Assuming poles grid; bezsurf = Surface.createBezierSurface(poles)"}, {"id": 69, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createExtrusionSurface(profile, direction)", "description": "Creates an extrusion surface", "example": "# Assuming profile wire and direction vector; extrusurf = Surface.createExtrusionSurface(profile, direction)", "source": "data/guide_en.txt", "text_content": "- `Surface.createExtrusionSurface(profile, direction)` - Creates an extrusion surface\n  *Example:* # Assuming profile wire and direction vector; extrusurf = Surface.createExtrusionSurface(profile, direction)"}, {"id": 70, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createRevolvedSurface(profile, center, axis, angle)", "description": "Creates a revolved surface", "example": "* `# Assuming profile wire, center, axis, angle; revsurf = Surface.createRevolvedSurface(profile, center, axis, 180)` # Revolve 180 degrees", "source": "data/guide_en.txt", "text_content": "- `Surface.createRevolvedSurface(profile, center, axis, angle)` - Creates a revolved surface\n  *Example:* * `# Assuming profile wire, center, axis, angle; revsurf = Surface.createRevolvedSurface(profile, center, axis, 180)` # Revolve 180 degrees"}, {"id": 71, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createLoftSurface([profiles])", "description": "Creates a loft surface", "example": "# Assuming list of profile wires; loftsurf = Surface.createLoftSurface(profiles)", "source": "data/guide_en.txt", "text_content": "- `Surface.createLoftSurface([profiles])` - Creates a loft surface\n  *Example:* # Assuming list of profile wires; loftsurf = Surface.createLoftSurface(profiles)"}, {"id": 72, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createSweptSurface(profile, path)", "description": "Creates a swept surface", "example": "# Assuming profile wire and path wire; sweptsurf = Surface.createSweptSurface(profile, path)", "source": "data/guide_en.txt", "text_content": "- `Surface.createSweptSurface(profile, path)` - Creates a swept surface\n  *Example:* # Assuming profile wire and path wire; sweptsurf = Surface.createSweptSurface(profile, path)"}, {"id": 73, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createBlendSurface(edge1, edge2)", "description": "Creates a blend surface (smooth connection between 2 edges)", "example": "# Assuming edge1 and edge2 Part.Edge objects; blendsurf = Surface.createBlendSurface(edge1, edge2)", "source": "data/guide_en.txt", "text_content": "- `Surface.createBlendSurface(edge1, edge2)` - Creates a blend surface (smooth connection between 2 edges)\n  *Example:* # Assuming edge1 and edge2 Part.Edge objects; blendsurf = Surface.createBlendSurface(edge1, edge2)"}, {"id": 74, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createFillingFace([edges])", "description": "Creates a filling face from edges (creates a patch face)", "example": "# Assuming list of edge objects forming a closed loop; fillsurf = Surface.createFillingFace(edges)", "source": "data/guide_en.txt", "text_content": "- `Surface.createFillingFace([edges])` - Creates a filling face from edges (creates a patch face)\n  *Example:* # Assuming list of edge objects forming a closed loop; fillsurf = Surface.createFillingFace(edges)"}, {"id": 75, "workbench": "3. Draft Workbench (Basic 2D and 3D Drawing)", "category": "3D Operations", "command_signature": "Surface.createCurveNetworkSurface([edges])", "description": "Creates a surface from a curve network", "example": "# Assuming list of edge objects forming a network; netsurf = Surface.createCurveNetworkSurface(edges)", "source": "data/guide_en.txt", "text_content": "- `Surface.createCurveNetworkSurface([edges])` - Creates a surface from a curve network\n  *Example:* # Assuming list of edge objects forming a network; netsurf = Surface.createCurveNetworkSurface(edges)"}, {"id": 76, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.createBox(length, width, height)", "description": "Creates a mesh box", "example": "mesh_box = Mesh.createBox(10, 20, 5)", "source": "data/guide_en.txt", "text_content": "- `Mesh.createBox(length, width, height)` - Creates a mesh box\n  *Example:* mesh_box = Mesh.createBox(10, 20, 5)"}, {"id": 77, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.createSphere(radius, [sampling])", "description": "Creates a mesh sphere", "example": "mesh_sphere = Mesh.createSphere(10)", "source": "data/guide_en.txt", "text_content": "- `Mesh.createSphere(radius, [sampling])` - Creates a mesh sphere\n  *Example:* mesh_sphere = Mesh.createSphere(10)"}, {"id": 78, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.createCylinder(radius, height, [sampling])", "description": "Creates a mesh cylinder", "example": "mesh_cyl = Mesh.createCylinder(5, 30)", "source": "data/guide_en.txt", "text_content": "- `Mesh.createCylinder(radius, height, [sampling])` - Creates a mesh cylinder\n  *Example:* mesh_cyl = Mesh.createCylinder(5, 30)"}, {"id": 79, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.createCone(radius1, radius2, height, [sampling])", "description": "Creates a mesh cone", "example": "mesh_cone = Mesh.createCone(5, 0, 15)", "source": "data/guide_en.txt", "text_content": "- `Mesh.createCone(radius1, radius2, height, [sampling])` - Creates a mesh cone\n  *Example:* mesh_cone = Mesh.createCone(5, 0, 15)"}, {"id": 80, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.createTorus(radius1, radius2, [sampling])", "description": "Creates a mesh torus", "example": "mesh_torus = Mesh.createTorus(10, 2)", "source": "data/guide_en.txt", "text_content": "- `Mesh.createTorus(radius1, radius2, [sampling])` - Creates a mesh torus\n  *Example:* mesh_torus = Mesh.createTorus(10, 2)"}, {"id": 81, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.createFromShape(shape, [linear_deflection], [angular_deflection])", "description": "Creates a mesh from a shape (Part object)", "example": "part_box = Part.makeBox(10,10,10); mesh_from_part = Mesh.createFromShape(part_box)", "source": "data/guide_en.txt", "text_content": "- `Mesh.createFromShape(shape, [linear_deflection], [angular_deflection])` - Creates a mesh from a shape (Part object)\n  *Example:* part_box = Part.makeBox(10,10,10); mesh_from_part = Mesh.createFromShape(part_box)"}, {"id": 82, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.flipNormals(mesh)", "description": "Flips the normal vectors of the mesh", "example": "# Assuming 'mesh' is a Mesh object; Mesh.flipNormals(mesh)", "source": "data/guide_en.txt", "text_content": "- `Mesh.flipNormals(mesh)` - Flips the normal vectors of the mesh\n  *Example:* # Assuming 'mesh' is a Mesh object; Mesh.flipNormals(mesh)"}, {"id": 83, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.harmonizeNormals(mesh)", "description": "Harmonizes normal vectors (outward or inward direction)", "example": "# Assuming 'mesh' is a Mesh object; Mesh.harmonizeNormals(mesh)", "source": "data/guide_en.txt", "text_content": "- `Mesh.harmonizeNormals(mesh)` - Harmonizes normal vectors (outward or inward direction)\n  *Example:* # Assuming 'mesh' is a Mesh object; Mesh.harmonizeNormals(mesh)"}, {"id": 84, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.smooth(mesh, [iterations])", "description": "Smooths the mesh", "example": "# Assuming 'mesh' is a Mesh object; smoothed_mesh = Mesh.smooth(mesh)", "source": "data/guide_en.txt", "text_content": "- `Mesh.smooth(mesh, [iterations])` - Smooths the mesh\n  *Example:* # Assuming 'mesh' is a Mesh object; smoothed_mesh = Mesh.smooth(mesh)"}, {"id": 85, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Mesh.refine(mesh)", "description": "Refines the mesh (adds detail)", "example": "# Assuming 'mesh' is a Mesh object; refined_mesh = Mesh.refine(mesh)", "source": "data/guide_en.txt", "text_content": "- `Mesh.refine(mesh)` - Refines the mesh (adds detail)\n  *Example:* # Assuming 'mesh' is a Mesh object; refined_mesh = Mesh.refine(mesh)"}, {"id": 86, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Points.createPoints([points])", "description": "Creates a point cloud", "example": "points_list = [(0,0,0), (1,1,1), (2,0,2)]; cloud = Points.createPoints(points_list)", "source": "data/guide_en.txt", "text_content": "- `Points.createPoints([points])` - Creates a point cloud\n  *Example:* points_list = [(0,0,0), (1,1,1), (2,0,2)]; cloud = Points.createPoints(points_list)"}, {"id": 87, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Points.convertToBSpline(points)", "description": "Converts points to a B-spline", "example": "# Assuming 'points' is a Points object; bspline = Points.convertToBSpline(points)", "source": "data/guide_en.txt", "text_content": "- `Points.convertToBSpline(points)` - Converts points to a B-spline\n  *Example:* # Assuming 'points' is a Points object; bspline = Points.convertToBSpline(points)"}, {"id": 88, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Points.show(points)", "description": "Displays the point cloud", "example": "# Assuming 'points' is a Points object; Points.show(points)", "source": "data/guide_en.txt", "text_content": "- `Points.show(points)` - Displays the point cloud\n  *Example:* # Assuming 'points' is a Points object; Points.show(points)"}, {"id": 89, "workbench": "6. <PERSON><PERSON> (Working with <PERSON><PERSON>)", "category": "", "command_signature": "Points.export(pointcloud, filename)", "description": "Exports the point cloud", "example": "# Assuming 'pointcloud' is a Points object; Points.export(pointcloud, \"cloud.asc\")", "source": "data/guide_en.txt", "text_content": "- `Points.export(pointcloud, filename)` - Exports the point cloud\n  *Example:* # Assuming 'pointcloud' is a Points object; Points.export(pointcloud, \"cloud.asc\")"}, {"id": 90, "workbench": "8. <PERSON> Workbench (Robot Simulation)", "category": "", "command_signature": "Robot.createRobot(name)", "description": "Creates a new robot", "example": "robot = Robot.createRobot(\"MyRobot\")", "source": "data/guide_en.txt", "text_content": "- `Robot.createRobot(name)` - Creates a new robot\n  *Example:* robot = Robot.createRobot(\"MyRobot\")"}, {"id": 91, "workbench": "8. <PERSON> Workbench (Robot Simulation)", "category": "", "command_signature": "Robot.createTrajectory(name)", "description": "Creates a trajectory", "example": "traj = Robot.createTrajectory(\"Path1\")", "source": "data/guide_en.txt", "text_content": "- `Robot.createTrajectory(name)` - Creates a trajectory\n  *Example:* traj = Robot.createTrajectory(\"Path1\")"}, {"id": 92, "workbench": "8. <PERSON> Workbench (Robot Simulation)", "category": "", "command_signature": "Robot.createEdge(p1, p2)", "description": "Creates an edge for the trajectory (usually internal use)", "example": "# Assuming p1, p2 are points/vectors; edge = Robot.createEdge(p1, p2)", "source": "data/guide_en.txt", "text_content": "- `Robot.createEdge(p1, p2)` - Creates an edge for the trajectory (usually internal use)\n  *Example:* # Assuming p1, p2 are points/vectors; edge = Robot.createEdge(p1, p2)"}, {"id": 93, "workbench": "8. <PERSON> Workbench (Robot Simulation)", "category": "", "command_signature": "Robot.createRobotTrajectory(robot, trajectory)", "description": "Creates a trajectory for the robot", "example": "# Assuming 'robot' and 'trajectory' objects; Robot.createRobotTrajectory(robot, trajectory)", "source": "data/guide_en.txt", "text_content": "- `Robot.createRobotTrajectory(robot, trajectory)` - Creates a trajectory for the robot\n  *Example:* # Assuming 'robot' and 'trajectory' objects; Robot.createRobotTrajectory(robot, trajectory)"}, {"id": 94, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch.makeWall(baseobj, [length], [width], [height])", "description": "Creates a wall", "example": "line = Draft.makeLine(App.Vector(0,0,0), App.Vector(5000,0,0)); wall = Arch.makeWall(line, height=3000, width=200)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeWall(baseobj, [length], [width], [height])` - Creates a wall\n  *Example:* line = Draft.makeLine(App.Vector(0,0,0), App.Vector(5000,0,0)); wall = Arch.makeWall(line, height=3000, width=200)"}, {"id": 95, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch.makeStructure(baseobj, [length], [width], [height])", "description": "Creates a structure (beam, column)", "example": "* `rect = Draft.makeRectangle(100, 100); struct = Arch.makeStructure(rect, height=3000)` # Column", "source": "data/guide_en.txt", "text_content": "- `Arch.makeStructure(baseobj, [length], [width], [height])` - Creates a structure (beam, column)\n  *Example:* * `rect = Draft.makeRectangle(100, 100); struct = Arch.makeStructure(rect, height=3000)` # Column"}, {"id": 96, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "<PERSON><PERSON>makeRoof(baseobj, [slopes])", "description": "Creates a roof", "example": "outline = Draft.makeRectangle(10000, 5000); roof = Arch.makeRoof(outline)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeRoof(baseobj, [slopes])` - Creates a roof\n  *Example:* outline = Draft.makeRectangle(10000, 5000); roof = Arch.makeRoof(outline)"}, {"id": 97, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch.makeFloor([objects])", "description": "Creates a floor", "example": "# Assuming 'walls' is a list of Arch Wall objects; floor = Arch.makeFloor(walls)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeFloor([objects])` - Creates a floor\n  *Example:* # Assuming 'walls' is a list of Arch Wall objects; floor = Arch.makeFloor(walls)"}, {"id": 98, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch.makeBuilding([objects])", "description": "Creates a building (groups floors)", "example": "# Assuming 'floors' is a list of Arch Floor objects; building = Arch.makeBuilding(floors)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeBuilding([objects])` - Creates a building (groups floors)\n  *Example:* # Assuming 'floors' is a list of Arch Floor objects; building = Arch.makeBuilding(floors)"}, {"id": 99, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch.makeSite([objects])", "description": "Creates a building site (groups buildings)", "example": "# Assuming 'buildings' is a list of Arch Building objects; site = Arch.makeSite(buildings)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeSite([objects])` - Creates a building site (groups buildings)\n  *Example:* # Assuming 'buildings' is a list of Arch Building objects; site = Arch.makeSite(buildings)"}, {"id": 100, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "<PERSON><PERSON>makeWindow(baseobj, [width], [height])", "description": "Creates a window (usually placed on a wall)", "example": "# Assuming 'wall_face' is a face on an Arch Wall; window = Arch.makeWindow(wall_face, width=1200, height=1000)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeWindow(baseobj, [width], [height])` - Creates a window (usually placed on a wall)\n  *Example:* # Assuming 'wall_face' is a face on an Arch Wall; window = Arch.makeWindow(wall_face, width=1200, height=1000)"}, {"id": 101, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "<PERSON><PERSON>makeDoor(baseobj, [width], [height])", "description": "Creates a door", "example": "# Assuming 'wall_face' is a face on an Arch Wall; door = Arch.makeDoor(wall_face, width=900, height=2100)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeDoor(baseobj, [width], [height])` - Creates a door\n  *Example:* # Assuming 'wall_face' is a face on an Arch Wall; door = Arch.makeDoor(wall_face, width=900, height=2100)"}, {"id": 102, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "<PERSON><PERSON>make<PERSON>(baseobj, [diameter])", "description": "Creates a pipe", "example": "wire = Draft.makeWire([App.Vector(0,0,0), App.Vector(0,5000,0)]); pipe = Arch.makePipe(wire, diameter=100)", "source": "data/guide_en.txt", "text_content": "- `Arch.makePipe(baseobj, [diameter])` - Creates a pipe\n  *Example:* wire = Draft.makeWire([App.Vector(0,0,0), App.Vector(0,5000,0)]); pipe = Arch.makePipe(wire, diameter=100)"}, {"id": 103, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch.makeStairs(baseobj, [length], [width], [height])", "description": "Creates stairs", "example": "baseline = Draft.makeLine(App.Vector(0,0,0), App.Vector(3000,0,0)); stairs = Arch.makeStairs(baseline, width=1000, height=2800)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeStairs(baseobj, [length], [width], [height])` - Creates stairs\n  *Example:* baseline = Draft.makeLine(App.Vector(0,0,0), App.Vector(3000,0,0)); stairs = Arch.makeStairs(baseline, width=1000, height=2800)"}, {"id": 104, "workbench": "10. <PERSON> (Architectural 3D Modeling)", "category": "", "command_signature": "Arch<PERSON>makeRebar(baseobj, [diameter], [spacing])", "description": "Creates rebar (in structures)", "example": "# Assuming 'structure' is an Arch Structure object; rebar = Arch.makeRebar(structure, diameter=12, spacing=150)", "source": "data/guide_en.txt", "text_content": "- `Arch.makeRebar(baseobj, [diameter], [spacing])` - Creates rebar (in structures)\n  *Example:* # Assuming 'structure' is an Arch Structure object; rebar = Arch.makeRebar(structure, diameter=12, spacing=150)"}, {"id": 105, "text_content": "Example Script: 3x3 rubik cube\n\nimport FreeCAD as App\nimport Part\nimport Import\nimport os\n\n# =========================\n# Document Setup\n# =========================\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"3x3 LEGO Brick Stack on 5x5 Plate\"\n\n# =========================\n# Parameters (mm)\n# =========================\nplate_length = 40.0\nplate_width  = 40.0\nplate_height = 3.2\n\nbrick_length = 24.0\nbrick_width  = 24.0\nbrick_height = 9.6   # Single brick height\nbrick_count  = 3     # Number of bricks stacked\n\n# Computed\nstack_height = brick_height * brick_count  # 28.8 mm\nbrick_z_pos  = plate_height                # Brick stack sits atop the plate\n\n# Center offsets to align center of brick stack with center of plate\nplate_center_x = plate_length / 2.0\nplate_center_y = plate_width  / 2.0\nbrick_center_x = brick_length / 2.0\nbrick_center_y = brick_width  / 2.0\n\n# =========================\n# Create 5x5 Plate\n# =========================\nplate = Part.makeBox(plate_length,\n                     plate_width,\n                     plate_height,\n                     App.Vector(0, 0, 0))\nplate_feature = doc.addObject(\"Part::Feature\", \"Plate5x5\")\nplate_feature.Label = \"5x5 Plate\"\nplate_feature.Shape = plate\n\n# =========================\n# Create 3x3 Brick Stack\n# =========================\n# Single box with total height = 3 bricks * brick_height\nbrick_stack = Part.makeBox(brick_length,\n                           brick_width,\n                           stack_height,\n                           App.Vector(\n                               plate_center_x - brick_center_x,\n                               plate_center_y - brick_center_y,\n                               brick_z_pos\n                           ))\nbrick_feature = doc.addObject(\"Part::Feature\", \"BrickStack3x3\")\nbrick_feature.Label = \"3x3 Brick Stack (3 high)\"\nbrick_feature.Shape = brick_stack\n\n# =========================\n# Boolean Fuse Operation\n# =========================\n# Fuse plate and brick stack into one assembly\nfused_shape = plate_feature.Shape.fuse(brick_feature.Shape)\nassembly = doc.addObject(\"Part::Feature\", \"StackedAssembly\")\nassembly.Label = \"Stacked Assembly\"\nassembly.Shape = fused_shape\n\n# Recompute document to finalize operations\ndoc.recompute()\n\n# =========================\n# Export to STEP\n# =========================\n# Prepare output directory\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir,\n                         \"3x3_LEGO_brick_stack_on_5x5_LEGO_plate.step\")\n\n# Export only the final assembly\nImport.export([assembly], step_path)\n\n# Final recompute for safety\ndoc.recompute()", "script_name": "3x3 rubik cube", "script_content": "import FreeCAD as App\nimport Part\nimport Import\nimport os\n\n# =========================\n# Document Setup\n# =========================\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"3x3 LEGO Brick Stack on 5x5 Plate\"\n\n# =========================\n# Parameters (mm)\n# =========================\nplate_length = 40.0\nplate_width  = 40.0\nplate_height = 3.2\n\nbrick_length = 24.0\nbrick_width  = 24.0\nbrick_height = 9.6   # Single brick height\nbrick_count  = 3     # Number of bricks stacked\n\n# Computed\nstack_height = brick_height * brick_count  # 28.8 mm\nbrick_z_pos  = plate_height                # Brick stack sits atop the plate\n\n# Center offsets to align center of brick stack with center of plate\nplate_center_x = plate_length / 2.0\nplate_center_y = plate_width  / 2.0\nbrick_center_x = brick_length / 2.0\nbrick_center_y = brick_width  / 2.0\n\n# =========================\n# Create 5x5 Plate\n# =========================\nplate = Part.makeBox(plate_length,\n                     plate_width,\n                     plate_height,\n                     App.Vector(0, 0, 0))\nplate_feature = doc.addObject(\"Part::Feature\", \"Plate5x5\")\nplate_feature.Label = \"5x5 Plate\"\nplate_feature.Shape = plate\n\n# =========================\n# Create 3x3 Brick Stack\n# =========================\n# Single box with total height = 3 bricks * brick_height\nbrick_stack = Part.makeBox(brick_length,\n                           brick_width,\n                           stack_height,\n                           App.Vector(\n                               plate_center_x - brick_center_x,\n                               plate_center_y - brick_center_y,\n                               brick_z_pos\n                           ))\nbrick_feature = doc.addObject(\"Part::Feature\", \"BrickStack3x3\")\nbrick_feature.Label = \"3x3 Brick Stack (3 high)\"\nbrick_feature.Shape = brick_stack\n\n# =========================\n# Boolean Fuse Operation\n# =========================\n# Fuse plate and brick stack into one assembly\nfused_shape = plate_feature.Shape.fuse(brick_feature.Shape)\nassembly = doc.addObject(\"Part::Feature\", \"StackedAssembly\")\nassembly.Label = \"Stacked Assembly\"\nassembly.Shape = fused_shape\n\n# Recompute document to finalize operations\ndoc.recompute()\n\n# =========================\n# Export to STEP\n# =========================\n# Prepare output directory\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir,\n                         \"3x3_LEGO_brick_stack_on_5x5_LEGO_plate.step\")\n\n# Export only the final assembly\nImport.export([assembly], step_path)\n\n# Final recompute for safety\ndoc.recompute()", "source": "data/example.txt"}, {"id": 106, "text_content": "Example Script: <PERSON><PERSON> with one hole\n\nimport FreeCAD as App\nimport Part\nimport Import\n\n# Parameters (for easy modification)\nR_CONE = 20.0     # Base radius of the cone\nH_CONE = 40.0     # Height of the cone\nR_HOLE = 5.0      # Radius of the cylindrical hole\nH_HOLE = 40.0     # Height of the cylindrical hole (same as cone)\n\n# Create a new document\ndoc = App.newDocument(\"GeneratedModel\")\nApp.ActiveDocument.Label = \"Cone_with_one_hole\"\n\n# -------------------------------------------------------------------\n# 1. Create the base cone\n# -------------------------------------------------------------------\n# Cone with base radius R_CONE, top radius 0, height H_CONE,\n# placed at the origin, axis aligned with Z\ncone_shape = Part.makeCone(R_CONE, 0.0, H_CONE,\n                           App.Vector(0, 0, 0),\n                           App.Vector(0, 0, 1))\ncone_obj = doc.addObject(\"Part::Feature\", \"Cone\")\ncone_obj.Shape = cone_shape\n\n# -------------------------------------------------------------------\n# 2. Create the cutting cylinder (the hole)\n# -------------------------------------------------------------------\n# Cylinder of radius R_HOLE, height H_HOLE,\n# placed at the origin, axis aligned with Z\ncyl_shape = Part.makeCylinder(R_HOLE, H_HOLE,\n                              App.Vector(0, 0, 0),\n                              App.Vector(0, 0, 1))\ncyl_obj = doc.addObject(\"Part::Feature\", \"Hole_Cylinder\")\ncyl_obj.Shape = cyl_shape\n\n# Recompute to register the shapes\ndoc.recompute()\n\n# -------------------------------------------------------------------\n# 3. Perform the boolean cut operation\n# -------------------------------------------------------------------\n# Subtract the cylinder from the cone\ncut_shape = cone_shape.cut(cyl_shape)\n\n# Create a new object to hold the result\nresult = doc.addObject(\"Part::Feature\", \"Cone_with_hole\")\nresult.Shape = cut_shape\n\n# -------------------------------------------------------------------\n# 4. Finalize the model\n# -------------------------------------------------------------------\ndoc.recompute()\n\n# -------------------------------------------------------------------\n# 5. Export to STEP\n# -------------------------------------------------------------------\n# Ensure the output directory 'cad_outputs' exists before exporting.\nstep_path = \"cad_outputs/Cone_with_one_hole.step\"\nImport.export([result], step_path)\n\n# End of script. The model \"Cone_with_one_hole\" has been created and exported.", "script_name": "Cone with one hole", "script_content": "import FreeCAD as App\nimport Part\nimport Import\n\n# Parameters (for easy modification)\nR_CONE = 20.0     # Base radius of the cone\nH_CONE = 40.0     # Height of the cone\nR_HOLE = 5.0      # Radius of the cylindrical hole\nH_HOLE = 40.0     # Height of the cylindrical hole (same as cone)\n\n# Create a new document\ndoc = App.newDocument(\"GeneratedModel\")\nApp.ActiveDocument.Label = \"Cone_with_one_hole\"\n\n# -------------------------------------------------------------------\n# 1. Create the base cone\n# -------------------------------------------------------------------\n# Cone with base radius R_CONE, top radius 0, height H_CONE,\n# placed at the origin, axis aligned with Z\ncone_shape = Part.makeCone(R_CONE, 0.0, H_CONE,\n                           App.Vector(0, 0, 0),\n                           App.Vector(0, 0, 1))\ncone_obj = doc.addObject(\"Part::Feature\", \"Cone\")\ncone_obj.Shape = cone_shape\n\n# -------------------------------------------------------------------\n# 2. Create the cutting cylinder (the hole)\n# -------------------------------------------------------------------\n# Cylinder of radius R_HOLE, height H_HOLE,\n# placed at the origin, axis aligned with Z\ncyl_shape = Part.makeCylinder(R_HOLE, H_HOLE,\n                              App.Vector(0, 0, 0),\n                              App.Vector(0, 0, 1))\ncyl_obj = doc.addObject(\"Part::Feature\", \"Hole_Cylinder\")\ncyl_obj.Shape = cyl_shape\n\n# Recompute to register the shapes\ndoc.recompute()\n\n# -------------------------------------------------------------------\n# 3. Perform the boolean cut operation\n# -------------------------------------------------------------------\n# Subtract the cylinder from the cone\ncut_shape = cone_shape.cut(cyl_shape)\n\n# Create a new object to hold the result\nresult = doc.addObject(\"Part::Feature\", \"Cone_with_hole\")\nresult.Shape = cut_shape\n\n# -------------------------------------------------------------------\n# 4. Finalize the model\n# -------------------------------------------------------------------\ndoc.recompute()\n\n# -------------------------------------------------------------------\n# 5. Export to STEP\n# -------------------------------------------------------------------\n# Ensure the output directory 'cad_outputs' exists before exporting.\nstep_path = \"cad_outputs/Cone_with_one_hole.step\"\nImport.export([result], step_path)\n\n# End of script. The model \"Cone_with_one_hole\" has been created and exported.", "source": "data/example.txt"}, {"id": 107, "text_content": "Example Script: Hexagonal\n\nimport FreeCAD as App\nimport Part\nimport Draft\nimport Import\nimport os\n\n# Parameters\nradius = 10.0    # Radius of circumscribed circle for hexagon\nheight = 5.0     # Extrusion height (prism height)\ndoc_name = \"GeneratedModel\"\noutput_dir = \"cad_outputs\"\nstep_filename = \"Hexagonal_Prism.step\"\n\n# Create new FreeCAD document\ndoc = App.newDocument(doc_name)\ndoc.Label = \"Hexagonal Prism\"\n\n# Create a regular hexagon (wire) in the XY plane\nhexagon = Draft.makePolygon(6, radius)\n# Ensure the document is up to date so Shape is available\ndoc.recompute()\n\n# Convert the hexagon wire into a planar face\nhex_face = Part.Face(hexagon.Shape)\n\n# Extrude the face along Z to create a prism\nprism_shape = hex_face.extrude(App.Vector(0, 0, height))\n\n# Add the prism to the document as a Part::Feature\nhex_prism = doc.addObject(\"Part::Feature\", \"HexagonalPrism\")\nhex_prism.Shape = prism_shape\n\n# Remove the original wire object to keep the document clean\ndoc.removeObject(hexagon.Name)\n\n# Final recompute to ensure the model is up to date\ndoc.recompute()\n\n# Prepare output directory\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# Export the hexagonal prism to STEP\nexport_path = os.path.join(output_dir, step_filename)\nImport.export([hex_prism], export_path)\n\n# Final document recompute\ndoc.recompute()", "script_name": "Hexagonal", "script_content": "import FreeCAD as App\nimport Part\nimport Draft\nimport Import\nimport os\n\n# Parameters\nradius = 10.0    # Radius of circumscribed circle for hexagon\nheight = 5.0     # Extrusion height (prism height)\ndoc_name = \"GeneratedModel\"\noutput_dir = \"cad_outputs\"\nstep_filename = \"Hexagonal_Prism.step\"\n\n# Create new FreeCAD document\ndoc = App.newDocument(doc_name)\ndoc.Label = \"Hexagonal Prism\"\n\n# Create a regular hexagon (wire) in the XY plane\nhexagon = Draft.makePolygon(6, radius)\n# Ensure the document is up to date so Shape is available\ndoc.recompute()\n\n# Convert the hexagon wire into a planar face\nhex_face = Part.Face(hexagon.Shape)\n\n# Extrude the face along Z to create a prism\nprism_shape = hex_face.extrude(App.Vector(0, 0, height))\n\n# Add the prism to the document as a Part::Feature\nhex_prism = doc.addObject(\"Part::Feature\", \"HexagonalPrism\")\nhex_prism.Shape = prism_shape\n\n# Remove the original wire object to keep the document clean\ndoc.removeObject(hexagon.Name)\n\n# Final recompute to ensure the model is up to date\ndoc.recompute()\n\n# Prepare output directory\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# Export the hexagonal prism to STEP\nexport_path = os.path.join(output_dir, step_filename)\nImport.export([hex_prism], export_path)\n\n# Final document recompute\ndoc.recompute()", "source": "data/example.txt"}, {"id": 108, "text_content": "Example Script: lego 3x3 brick\n\nimport os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# ------------------------------------------------------------\n# Script: Lego_3x3_Brick.py\n# Description: Generates a standard 3x3 LEGO brick (24×24×9.6 mm)\n#              with nine studs (Ø4.8×1.8 mm) and exports to STEP.\n# Environment: freecadcmd, FreeCAD 1.0.0 API\n# ------------------------------------------------------------\n\n# 1. Setup document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Lego_3x3_Brick\"\n\n# 2. Define key parameters (all units in mm)\nbrick_length = 24.0\nbrick_width  = 24.0\nbrick_height = 9.6\n\nstud_radius = 2.4\nstud_height = 1.8\n\n# Stud positions relative to brick origin (centered grid at top face)\nstud_positions = [\n    (-8.0, -8.0),\n    ( 0.0, -8.0),\n    ( 8.0, -8.0),\n    (-8.0,  0.0),\n    ( 0.0,  0.0),\n    ( 8.0,  0.0),\n    (-8.0,  8.0),\n    ( 0.0,  8.0),\n    ( 8.0,  8.0),\n]\n\n# 3. Create base brick body\nbrick_shape = Part.makeBox(brick_length,\n                           brick_width,\n                           brick_height,\n                           App.Vector(0, 0, 0))\n\n# 4. Create and fuse each stud onto the brick\n#    We'll build up the brick_shape variable by successive fuses\nfor idx, (sx, sy) in enumerate(stud_positions, start=1):\n    # Create a single stud (cylinder)\n    stud = Part.makeCylinder(\n        stud_radius,\n        stud_height,\n        App.Vector(sx + brick_length/2.0,\n                   sy + brick_width/2.0,\n                   brick_height),\n        App.Vector(0, 0, 1)\n    )\n    # Fuse stud into the brick\n    brick_shape = brick_shape.fuse(stud)\n\n# 5. Add final fused shape to the document\nbrick_obj = doc.addObject(\"Part::Feature\", \"Lego_3x3_Brick\")\nbrick_obj.Label = \"Lego 3x3 Brick\"\nbrick_obj.Shape = brick_shape\n\n# 6. Final recompute\ndoc.recompute()\n\n# 7. Export to STEP\noutput_dir = \"cad_outputs\"\nif not os.path.isdir(output_dir):\n    os.makedirs(output_dir)\n\nstep_path = os.path.join(output_dir, \"Lego_3x3_Brick.step\")\nImport.export([brick_obj], step_path)\n\n# 8. Final recompute (post-export, if needed)\ndoc.recompute()\n\n# End of script", "script_name": "lego 3x3 brick", "script_content": "import os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# ------------------------------------------------------------\n# Script: Lego_3x3_Brick.py\n# Description: Generates a standard 3x3 LEGO brick (24×24×9.6 mm)\n#              with nine studs (Ø4.8×1.8 mm) and exports to STEP.\n# Environment: freecadcmd, FreeCAD 1.0.0 API\n# ------------------------------------------------------------\n\n# 1. Setup document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Lego_3x3_Brick\"\n\n# 2. Define key parameters (all units in mm)\nbrick_length = 24.0\nbrick_width  = 24.0\nbrick_height = 9.6\n\nstud_radius = 2.4\nstud_height = 1.8\n\n# Stud positions relative to brick origin (centered grid at top face)\nstud_positions = [\n    (-8.0, -8.0),\n    ( 0.0, -8.0),\n    ( 8.0, -8.0),\n    (-8.0,  0.0),\n    ( 0.0,  0.0),\n    ( 8.0,  0.0),\n    (-8.0,  8.0),\n    ( 0.0,  8.0),\n    ( 8.0,  8.0),\n]\n\n# 3. Create base brick body\nbrick_shape = Part.makeBox(brick_length,\n                           brick_width,\n                           brick_height,\n                           App.Vector(0, 0, 0))\n\n# 4. Create and fuse each stud onto the brick\n#    We'll build up the brick_shape variable by successive fuses\nfor idx, (sx, sy) in enumerate(stud_positions, start=1):\n    # Create a single stud (cylinder)\n    stud = Part.makeCylinder(\n        stud_radius,\n        stud_height,\n        App.Vector(sx + brick_length/2.0,\n                   sy + brick_width/2.0,\n                   brick_height),\n        App.Vector(0, 0, 1)\n    )\n    # Fuse stud into the brick\n    brick_shape = brick_shape.fuse(stud)\n\n# 5. Add final fused shape to the document\nbrick_obj = doc.addObject(\"Part::Feature\", \"Lego_3x3_Brick\")\nbrick_obj.Label = \"Lego 3x3 Brick\"\nbrick_obj.Shape = brick_shape\n\n# 6. Final recompute\ndoc.recompute()\n\n# 7. Export to STEP\noutput_dir = \"cad_outputs\"\nif not os.path.isdir(output_dir):\n    os.makedirs(output_dir)\n\nstep_path = os.path.join(output_dir, \"Lego_3x3_Brick.step\")\nImport.export([brick_obj], step_path)\n\n# 8. Final recompute (post-export, if needed)\ndoc.recompute()\n\n# End of script", "source": "data/example.txt"}, {"id": 109, "text_content": "Example Script: L-shape Bracket\n\nimport os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# Ensure output directory exists\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# Create new document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"L-shaped_bracket\"\n\n# Parameters\nthickness = 6.0\nleg_length = 90.0\nhole_radius = 5.25\n# Horizontal plate: dimensions 90 x 90 x 6\n# Vertical plate: dimensions 6 x 90 x 90 (attached along one 90x6 face)\n\n# 1) Create the two primary boxes\nbox1 = Part.makeBox(leg_length, leg_length, thickness, App.Vector(0, 0, 0))\nbox2 = Part.makeBox(thickness, leg_length, leg_length, App.Vector(0, 0, 0))\n\n# 2) Fuse them into the L-shaped bracket\nbracket_shape = box1.fuse(box2)\n\n# 3) Define cylinder positions (center coordinates) for 8 holes\n#    First 4: through horizontal plate (axis along Z)\nh_hole_centers = [\n    (15.0, 15.0, thickness/2.0),\n    (75.0, 15.0, thickness/2.0),\n    (15.0, 75.0, thickness/2.0),\n    (75.0, 75.0, thickness/2.0),\n]\n#    Next 4: through vertical plate (axis along X)\nv_hole_centers = [\n    (thickness/2.0, 15.0, 15.0),\n    (thickness/2.0, 75.0, 15.0),\n    (thickness/2.0, 15.0, 75.0),\n    (thickness/2.0, 75.0, 75.0),\n]\n\n# 4) Cut holes in the bracket\n# Horizontal holes\nfor idx, (cx, cy, cz) in enumerate(h_hole_centers, start=1):\n    # For a hole through the horizontal plate, cylinder axis = Z.\n    # Base point z = cz - hole_height/2 = (thickness/2 - thickness/2) = 0\n    base_pt = App.Vector(cx, cy, 0.0)\n    cyl = Part.makeCylinder(hole_radius, thickness, base_pt, App.Vector(0, 0, 1))\n    bracket_shape = bracket_shape.cut(cyl)\n\n# Vertical holes\nfor idx, (cx, cy, cz) in enumerate(v_hole_centers, start=1):\n    # For a hole through the vertical plate, cylinder axis = X.\n    # Base point x = cx - hole_height/2 = 0\n    base_pt = App.Vector(0.0, cy, cz)\n    cyl = Part.makeCylinder(hole_radius, thickness, base_pt, App.Vector(1, 0, 0))\n    bracket_shape = bracket_shape.cut(cyl)\n\n# 5) Create a Part Feature and assign final shape\nbracket_obj = doc.addObject(\"Part::Feature\", \"Bracket\")\nbracket_obj.Label = \"L-shaped_bracket\"\nbracket_obj.Shape = bracket_shape\n\n# Recompute document to finalize geometry\ndoc.recompute()\n\n# 6) Export the final bracket to STEP\nstep_path = os.path.join(output_dir, \"L-shaped_bracket.step\")\nImport.export([bracket_obj], step_path)\n\nprint(f\"Model generated and exported to: {step_path}\"0)", "script_name": "L-shape Bracket", "script_content": "import os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# Ensure output directory exists\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# Create new document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"L-shaped_bracket\"\n\n# Parameters\nthickness = 6.0\nleg_length = 90.0\nhole_radius = 5.25\n# Horizontal plate: dimensions 90 x 90 x 6\n# Vertical plate: dimensions 6 x 90 x 90 (attached along one 90x6 face)\n\n# 1) Create the two primary boxes\nbox1 = Part.makeBox(leg_length, leg_length, thickness, App.Vector(0, 0, 0))\nbox2 = Part.makeBox(thickness, leg_length, leg_length, App.Vector(0, 0, 0))\n\n# 2) Fuse them into the L-shaped bracket\nbracket_shape = box1.fuse(box2)\n\n# 3) Define cylinder positions (center coordinates) for 8 holes\n#    First 4: through horizontal plate (axis along Z)\nh_hole_centers = [\n    (15.0, 15.0, thickness/2.0),\n    (75.0, 15.0, thickness/2.0),\n    (15.0, 75.0, thickness/2.0),\n    (75.0, 75.0, thickness/2.0),\n]\n#    Next 4: through vertical plate (axis along X)\nv_hole_centers = [\n    (thickness/2.0, 15.0, 15.0),\n    (thickness/2.0, 75.0, 15.0),\n    (thickness/2.0, 15.0, 75.0),\n    (thickness/2.0, 75.0, 75.0),\n]\n\n# 4) Cut holes in the bracket\n# Horizontal holes\nfor idx, (cx, cy, cz) in enumerate(h_hole_centers, start=1):\n    # For a hole through the horizontal plate, cylinder axis = Z.\n    # Base point z = cz - hole_height/2 = (thickness/2 - thickness/2) = 0\n    base_pt = App.Vector(cx, cy, 0.0)\n    cyl = Part.makeCylinder(hole_radius, thickness, base_pt, App.Vector(0, 0, 1))\n    bracket_shape = bracket_shape.cut(cyl)\n\n# Vertical holes\nfor idx, (cx, cy, cz) in enumerate(v_hole_centers, start=1):\n    # For a hole through the vertical plate, cylinder axis = X.\n    # Base point x = cx - hole_height/2 = 0\n    base_pt = App.Vector(0.0, cy, cz)\n    cyl = Part.makeCylinder(hole_radius, thickness, base_pt, App.Vector(1, 0, 0))\n    bracket_shape = bracket_shape.cut(cyl)\n\n# 5) Create a Part Feature and assign final shape\nbracket_obj = doc.addObject(\"Part::Feature\", \"Bracket\")\nbracket_obj.Label = \"L-shaped_bracket\"\nbracket_obj.Shape = bracket_shape\n\n# Recompute document to finalize geometry\ndoc.recompute()\n\n# 6) Export the final bracket to STEP\nstep_path = os.path.join(output_dir, \"L-shaped_bracket.step\")\nImport.export([bracket_obj], step_path)\n\nprint(f\"Model generated and exported to: {step_path}\"0)", "source": "data/example.txt"}, {"id": 110, "text_content": "Example Script: Airfoil NACA 2412\n\nimport FreeCAD as App\nimport Part\nimport math\nimport os\nimport Import\n\n# ---------------------------------------\n# Parameters (easy tuning at top)\n# ---------------------------------------\nchord     = 1.0    # chord length\nspan      = 5.0    # wing span (extrusion length)\nm         = 0.02   # max camber (2% of chord)\np         = 0.4    # camber position (40% of chord)\nt         = 0.12   # max thickness (12% of chord)\nn_points  = 100    # number of discretization points per surface\n\n# -------------------------------\n# 1) Create new, headless document\n# -------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"NACA_2412_Airfoil_Extrusion\"\n\n# ---------------------------------------\n# 2) Define function to compute NACA 4-digit coordinates\n# ---------------------------------------\ndef naca4_coordinates(m, p, t, chord, n):\n    \"\"\"\n    Returns two lists of App.Vector: (upper_surface_pts, lower_surface_pts)\n    representing the cambered airfoil profile in the X–Z plane.\n    \"\"\"\n    upper = []\n    lower = []\n    for i in range(n):\n        # Cosine spacing for better resolution near leading/trailing edge\n        beta = math.pi * i / (n - 1)\n        x = 0.5 * (1.0 - math.cos(beta)) * chord\n        # Thickness distribution (half-thickness)\n        yt = (t / 0.2) * chord * (\n              0.2969 * math.sqrt(x / chord)\n            - 0.1260 * (x / chord)\n            - 0.3516 * (x / chord)**2\n            + 0.2843 * (x / chord)**3\n            - 0.1015 * (x / chord)**4\n        )\n        # Camber line y_c and slope dy_c/dx\n        xc = x / chord\n        if xc <= p:\n            yc = (m / p**2) * (2*p*xc - xc**2) * chord\n            dyc_dx = (2*m / p**2) * (p - xc)\n        else:\n            yc = (m / (1 - p)**2) * ((1 - 2*p) + 2*p*xc - xc**2) * chord\n            dyc_dx = (2*m / (1 - p)**2) * (p - xc)\n        theta = math.atan(dyc_dx)\n        # Upper surface (x_u, z_u)\n        xu = x - yt * math.sin(theta)\n        zu = yc + yt * math.cos(theta)\n        # Lower surface (x_l, z_l)\n        xl = x + yt * math.sin(theta)\n        zl = yc - yt * math.cos(theta)\n        upper.append(App.Vector(xu, 0.0, zu))\n        lower.append(App.Vector(xl, 0.0, zl))\n    return upper, lower\n\n# ---------------------------------------\n# 3) Generate the airfoil outline\n# ---------------------------------------\npts_up, pts_lo = naca4_coordinates(m, p, t, chord, n_points)\n\n# Force exact closure at trailing edge\nte = App.Vector(chord, 0.0, 0.0)\npts_up[-1] = te\npts_lo[-1] = te\n\n# Build closed loop: start TE lower → LE → TE upper\noutline = list(reversed(pts_lo)) + pts_up\n\n# ---------------------------------------\n# 4) Create wire & face from outline\n# ---------------------------------------\nwire = Part.makePolygon(outline)\nface = Part.Face(wire)\n\n# ---------------------------------------\n# 5) Extrude face along Y-axis (span)\n# ---------------------------------------\nairfoil_solid = face.extrude(App.Vector(0.0, span, 0.0))\n\n# ---------------------------------------\n# 6) Add Part::Feature to document\n# ---------------------------------------\nairfoil_obj = doc.addObject(\"Part::Feature\", \"NACA2412_Airfoil\")\nairfoil_obj.Label = \"NACA 2412 Airfoil\"\nairfoil_obj.Shape = airfoil_solid\n\n# ---------------------------------------\n# 7) Final recompute\n# ---------------------------------------\ndoc.recompute()\n\n# ---------------------------------------\n# 8) Export to STEP\n# ---------------------------------------\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir, \"NACA_2412_Airfoil_Extrusion.step\")\nImport.export([airfoil_obj], step_path)\nprint(f\"Model exported to: {step_path}\")", "script_name": "Airfoil NACA 2412", "script_content": "import FreeCAD as App\nimport Part\nimport math\nimport os\nimport Import\n\n# ---------------------------------------\n# Parameters (easy tuning at top)\n# ---------------------------------------\nchord     = 1.0    # chord length\nspan      = 5.0    # wing span (extrusion length)\nm         = 0.02   # max camber (2% of chord)\np         = 0.4    # camber position (40% of chord)\nt         = 0.12   # max thickness (12% of chord)\nn_points  = 100    # number of discretization points per surface\n\n# -------------------------------\n# 1) Create new, headless document\n# -------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"NACA_2412_Airfoil_Extrusion\"\n\n# ---------------------------------------\n# 2) Define function to compute NACA 4-digit coordinates\n# ---------------------------------------\ndef naca4_coordinates(m, p, t, chord, n):\n    \"\"\"\n    Returns two lists of App.Vector: (upper_surface_pts, lower_surface_pts)\n    representing the cambered airfoil profile in the X–Z plane.\n    \"\"\"\n    upper = []\n    lower = []\n    for i in range(n):\n        # Cosine spacing for better resolution near leading/trailing edge\n        beta = math.pi * i / (n - 1)\n        x = 0.5 * (1.0 - math.cos(beta)) * chord\n        # Thickness distribution (half-thickness)\n        yt = (t / 0.2) * chord * (\n              0.2969 * math.sqrt(x / chord)\n            - 0.1260 * (x / chord)\n            - 0.3516 * (x / chord)**2\n            + 0.2843 * (x / chord)**3\n            - 0.1015 * (x / chord)**4\n        )\n        # Camber line y_c and slope dy_c/dx\n        xc = x / chord\n        if xc <= p:\n            yc = (m / p**2) * (2*p*xc - xc**2) * chord\n            dyc_dx = (2*m / p**2) * (p - xc)\n        else:\n            yc = (m / (1 - p)**2) * ((1 - 2*p) + 2*p*xc - xc**2) * chord\n            dyc_dx = (2*m / (1 - p)**2) * (p - xc)\n        theta = math.atan(dyc_dx)\n        # Upper surface (x_u, z_u)\n        xu = x - yt * math.sin(theta)\n        zu = yc + yt * math.cos(theta)\n        # Lower surface (x_l, z_l)\n        xl = x + yt * math.sin(theta)\n        zl = yc - yt * math.cos(theta)\n        upper.append(App.Vector(xu, 0.0, zu))\n        lower.append(App.Vector(xl, 0.0, zl))\n    return upper, lower\n\n# ---------------------------------------\n# 3) Generate the airfoil outline\n# ---------------------------------------\npts_up, pts_lo = naca4_coordinates(m, p, t, chord, n_points)\n\n# Force exact closure at trailing edge\nte = App.Vector(chord, 0.0, 0.0)\npts_up[-1] = te\npts_lo[-1] = te\n\n# Build closed loop: start TE lower → LE → TE upper\noutline = list(reversed(pts_lo)) + pts_up\n\n# ---------------------------------------\n# 4) Create wire & face from outline\n# ---------------------------------------\nwire = Part.makePolygon(outline)\nface = Part.Face(wire)\n\n# ---------------------------------------\n# 5) Extrude face along Y-axis (span)\n# ---------------------------------------\nairfoil_solid = face.extrude(App.Vector(0.0, span, 0.0))\n\n# ---------------------------------------\n# 6) Add Part::Feature to document\n# ---------------------------------------\nairfoil_obj = doc.addObject(\"Part::Feature\", \"NACA2412_Airfoil\")\nairfoil_obj.Label = \"NACA 2412 Airfoil\"\nairfoil_obj.Shape = airfoil_solid\n\n# ---------------------------------------\n# 7) Final recompute\n# ---------------------------------------\ndoc.recompute()\n\n# ---------------------------------------\n# 8) Export to STEP\n# ---------------------------------------\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir, \"NACA_2412_Airfoil_Extrusion.step\")\nImport.export([airfoil_obj], step_path)\nprint(f\"Model exported to: {step_path}\")", "source": "data/example.txt"}, {"id": 111, "text_content": "Example Script: Rectangular with 5 straight through holes\n\nimport os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# ------------------------------------------------------------\n# Script: Rectangular_block_with_5_straight_through_holes.py\n# Description: Generate a rectangular block with 5 straight\n# through holes, then export as STEP. Compatible with freecadcmd.\n# ------------------------------------------------------------\n\n# Create document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular block with 5 straight through holes\"\n\n# Parameters (for easy modification)\nlength = 100.0   # X dimension of the box\nwidth  = 50.0    # Y dimension of the box\nheight = 20.0    # Z dimension of the box\n\nhole_radius = 5.0\nhole_height = height  # through-hole\n\n# X-positions of holes (evenly spaced)\nhole_x_positions = [10.0, 30.0, 50.0, 70.0, 90.0]\nhole_y = width / 2.0   # centered in Y\nhole_z = 0.0           # start at base of the box\n\n# 1) Create the main rectangular block\nmain_box_shape = Part.makeBox(length, width, height, App.Vector(0, 0, 0))\n# We'll apply boolean cuts to this shape iteratively\ncurrent_shape = main_box_shape\n\n# 2) Create cylinders for holes and subtract them one by one\nfor idx, x in enumerate(hole_x_positions, start=1):\n    cyl = Part.makeCylinder(hole_radius, hole_height, App.Vector(x, hole_y, hole_z))\n    # Subtract this hole from the current block shape\n    current_shape = current_shape.cut(cyl)\n\n# 3) Add final shape to document\nfinal_obj = doc.addObject(\"Part::Feature\", \"Block_with_5_Holes\")\nfinal_obj.Label = \"RectBlockWithHoles\"\nfinal_obj.Shape = current_shape\n\n# 4) Recompute document to finalize operations\ndoc.recompute()\n\n# 5) Ensure export directory exists\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# 6) Export the final object to STEP\nstep_path = os.path.join(output_dir,\n    \"Rectangular_block_with_5_straight_through_holes.step\")\nImport.export([final_obj], step_path)\n\n# Final recompute (just in case)\ndoc.recompute()", "script_name": "Rectangular with 5 straight through holes", "script_content": "import os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# ------------------------------------------------------------\n# Script: Rectangular_block_with_5_straight_through_holes.py\n# Description: Generate a rectangular block with 5 straight\n# through holes, then export as STEP. Compatible with freecadcmd.\n# ------------------------------------------------------------\n\n# Create document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular block with 5 straight through holes\"\n\n# Parameters (for easy modification)\nlength = 100.0   # X dimension of the box\nwidth  = 50.0    # Y dimension of the box\nheight = 20.0    # Z dimension of the box\n\nhole_radius = 5.0\nhole_height = height  # through-hole\n\n# X-positions of holes (evenly spaced)\nhole_x_positions = [10.0, 30.0, 50.0, 70.0, 90.0]\nhole_y = width / 2.0   # centered in Y\nhole_z = 0.0           # start at base of the box\n\n# 1) Create the main rectangular block\nmain_box_shape = Part.makeBox(length, width, height, App.Vector(0, 0, 0))\n# We'll apply boolean cuts to this shape iteratively\ncurrent_shape = main_box_shape\n\n# 2) Create cylinders for holes and subtract them one by one\nfor idx, x in enumerate(hole_x_positions, start=1):\n    cyl = Part.makeCylinder(hole_radius, hole_height, App.Vector(x, hole_y, hole_z))\n    # Subtract this hole from the current block shape\n    current_shape = current_shape.cut(cyl)\n\n# 3) Add final shape to document\nfinal_obj = doc.addObject(\"Part::Feature\", \"Block_with_5_Holes\")\nfinal_obj.Label = \"RectBlockWithHoles\"\nfinal_obj.Shape = current_shape\n\n# 4) Recompute document to finalize operations\ndoc.recompute()\n\n# 5) Ensure export directory exists\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# 6) Export the final object to STEP\nstep_path = os.path.join(output_dir,\n    \"Rectangular_block_with_5_straight_through_holes.step\")\nImport.export([final_obj], step_path)\n\n# Final recompute (just in case)\ndoc.recompute()", "source": "data/example.txt"}, {"id": 112, "text_content": "Example Script: Rectangular with central and corner holes\n\nimport os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# Ensure output directory exists\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# Create a new document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_block_with_central_and_corner_through_holes\"\n\n# Parameters\nlength = 100.0\nwidth = 50.0\nheight = 10.0\nhole_radius = 5.0\n# Positions for holes (Z is 0 so they go through entire height)\ncentral_hole_pos = App.Vector(length/2.0, width/2.0, 0)\ncorner_offsets = [\n    App.Vector(10.0, 10.0, 0),\n    App.Vector(length - 10.0, 10.0, 0),\n    App.Vector(10.0, width - 10.0, 0),\n    App.Vector(length - 10.0, width - 10.0, 0),\n]\n\n# 1) Create the base block\nbase_box = Part.makeBox(length, width, height, App.Vector(0, 0, 0))\nbase_obj = doc.addObject(\"Part::Feature\", \"BaseBlock\")\nbase_obj.Shape = base_box\n\n# 2) Create the central through-hole cylinder\ncyl_central = Part.makeCylinder(hole_radius, height, central_hole_pos, App.Vector(0, 0, 1))\ncyl_central_obj = doc.addObject(\"Part::Feature\", \"CentralHole\")\ncyl_central_obj.Shape = cyl_central\n\n# Cut central hole\ncut1 = base_obj.Shape.cut(cyl_central_obj.Shape)\nbase_obj.Shape = cut1\ndoc.recompute()\n\n# 3) Create and cut corner holes one by one\nfor idx, pos in enumerate(corner_offsets, start=1):\n    cyl = Part.makeCylinder(hole_radius, height, pos, App.Vector(0, 0, 1))\n    cyl_obj = doc.addObject(\"Part::Feature\", f\"CornerHole{idx}\")\n    cyl_obj.Shape = cyl\n    # Perform cut on the current base shape\n    new_shape = base_obj.Shape.cut(cyl_obj.Shape)\n    base_obj.Shape = new_shape\n    doc.recompute()\n\n# Rename final object\nbase_obj.Label = \"FinalBlock\"\n\n# Final recompute\ndoc.recompute()\n\n# Export to STEP\nstep_filepath = os.path.join(output_dir,\n    \"Rectangular_block_with_central_and_corner_through_holes.step\")\nImport.export([base_obj], step_filepath)\n\nprint(f\"Model exported to {step_filepath}\")\n\n#Example Rectangular with \nimport FreeCAD as App\nimport Part\nimport math\nimport os\nimport Import\n\n# Create a new document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_box_with_three_triangularly_arranged_through_holes\"\n\n# Parameters\nlength = 100.0\nwidth = 60.0\nheight = 20.0\nhole_radius = 5.0\nside_length = 30.0\n\n# Compute box center in XY\ncenter = App.Vector(length/2.0, width/2.0, 0.0)\n\n# Calculate distance from centroid to triangle vertices\nd = side_length / math.sqrt(3.0)\nangles_deg = [90.0, 210.0, 330.0]\n\n# Generate hole positions relative to box center\nhole_positions = []\nfor angle in angles_deg:\n    rad = math.radians(angle)\n    x = d * math.cos(rad)\n    y = d * math.sin(rad)\n    hole_positions.append(App.Vector(x, y, 0.0))\n\n# Create the base box\nbox_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))\nbox_obj = doc.addObject(\"Part::Feature\", \"Box\")\nbox_obj.Shape = box_shape\n\n# Create cylinders for holes\ncylinder_shapes = []\nfor idx, pos in enumerate(hole_positions, start=1):\n    cyl = Part.makeCylinder(hole_radius, height, center + pos)\n    cyl_obj = doc.addObject(\"Part::Feature\", f\"Hole_Cylinder_{idx}\")\n    cyl_obj.Shape = cyl\n    cylinder_shapes.append(cyl)\n\n# Perform boolean cuts\nresult_shape = box_obj.Shape\nfor cyl in cylinder_shapes:\n    result_shape = result_shape.cut(cyl)\n\n# Create the final feature\nfinal_obj = doc.addObject(\"Part::Feature\", \"box_with_3_holes\")\nfinal_obj.Shape = result_shape\n\n# Recompute to finalize\ndoc.recompute()\n\n# Export to STEP\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir, \"Rectangular_box_with_three_triangularly_arranged_through_holes.step\")\nImport.export([final_obj], step_path)", "script_name": "Rectangular with central and corner holes", "script_content": "import os\nimport FreeCAD as App\nimport Part\nimport Import\n\n# Ensure output directory exists\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\n\n# Create a new document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_block_with_central_and_corner_through_holes\"\n\n# Parameters\nlength = 100.0\nwidth = 50.0\nheight = 10.0\nhole_radius = 5.0\n# Positions for holes (Z is 0 so they go through entire height)\ncentral_hole_pos = App.Vector(length/2.0, width/2.0, 0)\ncorner_offsets = [\n    App.Vector(10.0, 10.0, 0),\n    App.Vector(length - 10.0, 10.0, 0),\n    App.Vector(10.0, width - 10.0, 0),\n    App.Vector(length - 10.0, width - 10.0, 0),\n]\n\n# 1) Create the base block\nbase_box = Part.makeBox(length, width, height, App.Vector(0, 0, 0))\nbase_obj = doc.addObject(\"Part::Feature\", \"BaseBlock\")\nbase_obj.Shape = base_box\n\n# 2) Create the central through-hole cylinder\ncyl_central = Part.makeCylinder(hole_radius, height, central_hole_pos, App.Vector(0, 0, 1))\ncyl_central_obj = doc.addObject(\"Part::Feature\", \"CentralHole\")\ncyl_central_obj.Shape = cyl_central\n\n# Cut central hole\ncut1 = base_obj.Shape.cut(cyl_central_obj.Shape)\nbase_obj.Shape = cut1\ndoc.recompute()\n\n# 3) Create and cut corner holes one by one\nfor idx, pos in enumerate(corner_offsets, start=1):\n    cyl = Part.makeCylinder(hole_radius, height, pos, App.Vector(0, 0, 1))\n    cyl_obj = doc.addObject(\"Part::Feature\", f\"CornerHole{idx}\")\n    cyl_obj.Shape = cyl\n    # Perform cut on the current base shape\n    new_shape = base_obj.Shape.cut(cyl_obj.Shape)\n    base_obj.Shape = new_shape\n    doc.recompute()\n\n# Rename final object\nbase_obj.Label = \"FinalBlock\"\n\n# Final recompute\ndoc.recompute()\n\n# Export to STEP\nstep_filepath = os.path.join(output_dir,\n    \"Rectangular_block_with_central_and_corner_through_holes.step\")\nImport.export([base_obj], step_filepath)\n\nprint(f\"Model exported to {step_filepath}\")\n\n#Example Rectangular with \nimport FreeCAD as App\nimport Part\nimport math\nimport os\nimport Import\n\n# Create a new document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_box_with_three_triangularly_arranged_through_holes\"\n\n# Parameters\nlength = 100.0\nwidth = 60.0\nheight = 20.0\nhole_radius = 5.0\nside_length = 30.0\n\n# Compute box center in XY\ncenter = App.Vector(length/2.0, width/2.0, 0.0)\n\n# Calculate distance from centroid to triangle vertices\nd = side_length / math.sqrt(3.0)\nangles_deg = [90.0, 210.0, 330.0]\n\n# Generate hole positions relative to box center\nhole_positions = []\nfor angle in angles_deg:\n    rad = math.radians(angle)\n    x = d * math.cos(rad)\n    y = d * math.sin(rad)\n    hole_positions.append(App.Vector(x, y, 0.0))\n\n# Create the base box\nbox_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))\nbox_obj = doc.addObject(\"Part::Feature\", \"Box\")\nbox_obj.Shape = box_shape\n\n# Create cylinders for holes\ncylinder_shapes = []\nfor idx, pos in enumerate(hole_positions, start=1):\n    cyl = Part.makeCylinder(hole_radius, height, center + pos)\n    cyl_obj = doc.addObject(\"Part::Feature\", f\"Hole_Cylinder_{idx}\")\n    cyl_obj.Shape = cyl\n    cylinder_shapes.append(cyl)\n\n# Perform boolean cuts\nresult_shape = box_obj.Shape\nfor cyl in cylinder_shapes:\n    result_shape = result_shape.cut(cyl)\n\n# Create the final feature\nfinal_obj = doc.addObject(\"Part::Feature\", \"box_with_3_holes\")\nfinal_obj.Shape = result_shape\n\n# Recompute to finalize\ndoc.recompute()\n\n# Export to STEP\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir, \"Rectangular_box_with_three_triangularly_arranged_through_holes.step\")\nImport.export([final_obj], step_path)", "source": "data/example.txt"}, {"id": 113, "text_content": "Example Script: Rectangular with 4 corner holes and chamfered edges\n\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\n\n# Create a new, headless FreeCAD document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_box_with_4_corner_holes_and_chamfered_edges\"\n\n# -----------------------------\n# Parameters (for easy tuning)\n# -----------------------------\nlength       = 100.0   # X dimension of the box\nwidth        = 60.0    # Y dimension of the box\nheight       = 20.0    # Z dimension of the box\nhole_radius  = 5.0     # Radius of each corner hole\nchamfer_size = 2.0     # Size of the chamfer on all external edges\n\n# --------------------------------------\n# 1) Create the base rectangular box\n# --------------------------------------\nbase_box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))\n\n# --------------------------------------\n# 2) Chamfer all external edges of box\n# --------------------------------------\n# Get all edges of the box\nedges = base_box_shape.Edges\n# Apply chamfer using a single radius for all edges\nchamfered_box = base_box_shape.makeChamfer(chamfer_size, edges)\n\n# --------------------------------------\n# 3) Define corner-hole cylinder positions\n# --------------------------------------\nhole_positions = [\n    App.Vector(10.0, 10.0, 0.0),\n    App.Vector(length - 10.0, 10.0, 0.0),\n    App.Vector(10.0, width  - 10.0, 0.0),\n    App.Vector(length - 10.0, width  - 10.0, 0.0),\n]\n\n# --------------------------------------\n# 4) Subtract four through-holes\n# --------------------------------------\nresult_shape = chamfered_box\nfor idx, pos in enumerate(hole_positions, start=1):\n    cyl = Part.makeCylinder(hole_radius, height, pos, App.Vector(0.0, 0.0, 1.0))\n    result_shape = result_shape.cut(cyl)\n\n# --------------------------------------\n# 5) Create the final Part::Feature object\n# --------------------------------------\nfinal_obj = doc.addObject(\"Part::Feature\",\n                          \"Rectangular_Box_With_4_Corner_Holes_and_Chamfered_Edges\")\nfinal_obj.Shape = result_shape\n\n# Recompute document to finalize all operations\ndoc.recompute()\n\n# --------------------------------------\n# 6) Export the final shape to STEP\n# --------------------------------------\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(\n    output_dir,\n    \"Rectangular_box_with_4_corner_holes_and_chamfered_edges.step\"\n)\nImport.export([final_obj], step_path)\n\n# --------------------------------------\n# Script complete: the STEP file is ready\n# --------------------------------------\n\n#Example rectangular with chamfered edges\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\n\n# -----------------------------\n# Parameters (for easy tuning)\n# -----------------------------\nlength       = 100.0   # X dimension of the box\nwidth        = 50.0    # Y dimension of the box\nheight       = 20.0    # Z dimension of the box\nchamfer_size = 2.0     # Chamfer size on all external edges (2 mm at 45°)\n\n# -----------------------------\n# 1) Create a new, headless document\n# -----------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_block_with_chamfered_edges\"\n\n# -----------------------------\n# 2) Create the base rectangular box\n# -----------------------------\nbase_box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))\nbase_box_obj   = doc.addObject(\"Part::Feature\", \"BaseBox\")\nbase_box_obj.Shape = base_box_shape\n\n# -----------------------------\n# 3) Apply chamfer to all external edges\n# -----------------------------\n# Collect all edges of the box\nedges = base_box_shape.Edges\n\n# Perform chamfer operation\nchamfered_shape = base_box_shape.makeChamfer(chamfer_size, edges)\n\n# Create a new feature for the chamfered box\nchamfered_box_obj = doc.addObject(\"Part::Feature\", \"ChamferedBox\")\nchamfered_box_obj.Shape = chamfered_shape\n\n# -----------------------------\n# 4) Finalize the model\n# -----------------------------\ndoc.recompute()\n\n# -----------------------------\n# 5) Export to STEP\n# -----------------------------\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir, \"Rectangular_block_with_chamfered_edges.step\")\nImport.export([chamfered_box_obj], step_path)\n\n# Final recompute to ensure integrity\ndoc.recompute()\n\n#Example Perforlated sheet C20_U40_1200x600\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport Import\nimport Mesh\nimport os\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"C20_U40_1200x600_Optimized\"  # DO NOT CHANGE THIS VALUE (Added _Optimized for clarity)\n\n# =========================\n# Document Setup\n# =========================\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# =========================\n# Parameters (mm)\n# =========================\nlength = 1200.0  # X dimension (length)\nwidth  =  600.0  # Y dimension (width)\nheight =    2.0  # Z dimension (thickness)\n\n# =========================\n# Create the metal plate\n# =========================\nbase_plate = Part.makeBox(length,\n                          width,\n                          height,\n                          App.Vector(0.0, 0.0, 0.0))\n\n# =========================\n# Drill square holes (evenly distribute holes with equal margins on both edges)\n#   - square side: 20 mm\n#   - center spacing: 40 mm in X and Y\n# =========================\nhole_side = 20.0\nspacing   = 40.0\ntool_height = height * 1.5 # Make tool slightly thicker for robust cutting\n\n# Compute number of holes in X direction\nif length < hole_side:\n    n_x = 0\nelse:\n    n_x = int((length - hole_side) // spacing) + 1\n\n# Total span occupied by holes\nif n_x > 0:\n    span_x = (n_x - 1) * spacing + hole_side\n    # Margin on each side to center the holes\n    margin_x = (length - span_x) / 2.0\nelse:\n    margin_x = length / 2.0 # Or handle as no holes\n\n# Build X centers array\nx_centers = []\nif n_x > 0:\n    x_centers = [\n        margin_x + hole_side / 2.0 + i * spacing\n        for i in range(n_x)\n    ]\n\n# Compute number of holes in Y direction\nif width < hole_side:\n    n_y = 0\nelse:\n    n_y = int((width - hole_side) // spacing) + 1\n\nif n_y > 0:\n    span_y = (n_y - 1) * spacing + hole_side\n    margin_y = (width - span_y) / 2.0\nelse:\n    margin_y = width / 2.0 # Or handle as no holes\n\n# Build Y centers array\ny_centers = []\nif n_y > 0:\n    y_centers = [\n        margin_y + hole_side / 2.0 + j * spacing\n        for j in range(n_y)\n    ]\n\n# Start with the base plate shape\nresult_shape = base_plate\nall_hole_tools = [] # List to store all hole cutting tools\n\n# For each hole center, create a square prism and add to list\nif n_x > 0 and n_y > 0:\n    for cx in x_centers:\n        for cy in y_centers:\n            # Boundary check (optional, as centering logic should handle it, but good for robustness)\n            # Ensure the entire hole is within the plate boundaries\n            min_hole_x = cx - hole_side / 2.0\n            max_hole_x = cx + hole_side / 2.0\n            min_hole_y = cy - hole_side / 2.0\n            max_hole_y = cy + hole_side / 2.0\n            tolerance = 1e-6\n\n            if (min_hole_x >= 0.0 - tolerance and\n                max_hole_x <= length + tolerance and\n                min_hole_y >= 0.0 - tolerance and\n                max_hole_y <= width + tolerance):\n\n                hole_corner = App.Vector(cx - hole_side / 2.0,\n                                         cy - hole_side / 2.0,\n                                         (height - tool_height) / 2.0) # Center tool vertically for good measure\n                hole_box = Part.makeBox(hole_side,\n                                        hole_side,\n                                        tool_height, # Use slightly thicker tool\n                                        hole_corner)\n                all_hole_tools.append(hole_box)\n\n# If there are tools, create a compound and cut once\nif all_hole_tools:\n    holes_compound = Part.Compound(all_hole_tools)\n    result_shape = base_plate.cut(holes_compound)\n\n# =========================\n# Create the final Part Feature\n# =========================\nplate_obj = doc.addObject(\"Part::Feature\", \"MetalPlateWithHoles\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\n\n# Recompute to finalize geometry\ndoc.recompute()\n\n# =========================\n# Export settings (absolute paths)\n# =========================\n# Determine script directory robustly\nif \"__file__\" in locals() or \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    # Fallback for environments where __file__ is not defined (e.g. FreeCAD GUI Python console)\n    script_dir = os.getcwd() \n\noutput_dir_abs = os.path.abspath(os.path.join(script_dir,\n                                              '..',\n                                              'cad_outputs_generated'))\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(output_dir_abs,\n                                 f'{sanitized_title}.step')\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(output_dir_abs,\n                                f'{sanitized_title}.obj')\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\nprint(f\"Generated {sanitized_title}\")\n\n\n#Example perforated sheet LR5x20 Z9x24 600x300\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"LR5x20_Z9x24_600x300\"  # DO NOT CHANGE THIS VALUE\n\n# =========================\n# Document Setup\n# =========================\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# =========================\n# Parameters (mm) for the plate\n# =========================\nplate_length = 600.0  # X dimension\nplate_width  = 300.0  # Y dimension\nplate_thickness = 2.0  # Z dimension\n\n# =========================\n# Create the base metal plate\n# =========================\nbase_plate_shape = Part.makeBox(plate_length,\n                                plate_width,\n                                plate_thickness,\n                                App.Vector(0.0, 0.0, 0.0))\n\n# =========================\n# Parameters for the obround holes (e.g., LR5x20 type)\n# =========================\n# \"Horizontally oriented\" means length of obround is along plate's X-axis.\nobround_hole_length = 20.0  # Total length of the obround (along plate X)\nobround_hole_width  = 5.0   # Total width of the obround (along plate Y)\n\n# =========================\n# Parameters for the staggered pattern\n# =========================\n# \"horizontal spacing between adjacent holes\" -> pitch_x (center-to-center in X for non-staggered elements)\n# \"vertical offset between rows\" -> pitch_y (center-to-center in Y between rows of holes)\n# These pitch values can be adjusted to change pattern density.\npitch_x = 24.0  # Center-to-center spacing in X-direction for holes in the same conceptual line\npitch_y = 9.0  # Center-to-center spacing in Y-direction between rows of holes\n\n# =========================\n# Helper function to create a single obround tool\n# Creates an obround in the XY plane, centered at (cx, cy), extruded by 'thickness_z' along Z.\n# total_len is aligned with the X-axis, obround_h with the Y-axis.\n# =========================\ndef make_single_obround_tool(cx, cy, total_len, obround_h, thickness_z):\n    radius = obround_h / 2.0\n    # Length of the central rectangular part of the obround\n    rect_len = total_len - 2 * radius \n\n    if rect_len <= 1e-6: # If effectively a circle (e.g. total_len == obround_h)\n        tool_shape = Part.makeCylinder(radius, thickness_z, \n                                       App.Vector(cx, cy, 0), \n                                       App.Vector(0,0,1)) # Axis along Z\n    else:\n        # Central rectangular prism\n        rect_base_pos = App.Vector(cx - rect_len / 2.0,\n                                   cy - obround_h / 2.0,\n                                   0.0)\n        rect_box = Part.makeBox(rect_len, obround_h, thickness_z, rect_base_pos)\n\n        # Cylinder at the \"positive X\" end of the rectangle\n        cyl1_center_pos = App.Vector(cx + rect_len / 2.0, cy, 0.0)\n        cyl1 = Part.makeCylinder(radius, thickness_z, cyl1_center_pos, App.Vector(0,0,1))\n\n        # Cylinder at the \"negative X\" end of the rectangle\n        cyl2_center_pos = App.Vector(cx - rect_len / 2.0, cy, 0.0)\n        cyl2 = Part.makeCylinder(radius, thickness_z, cyl2_center_pos, App.Vector(0,0,1))\n        \n        tool_shape = rect_box.fuse(cyl1).fuse(cyl2)\n    return tool_shape\n\n# =========================\n# Calculate hole distribution: number of rows/columns and margins\n# =========================\n\n# Max number of holes in X direction (columns of holes) if not staggered\nif plate_length < obround_hole_length:\n    n_cols_max = 0\nelse:\n    n_cols_max = int((plate_length - obround_hole_length) / pitch_x) + 1\n    \n# Max number of holes in Y direction (rows of holes)\nif plate_width < obround_hole_width:\n    n_rows_max = 0\nelse:\n    n_rows_max = int((plate_width - obround_hole_width) / pitch_y) + 1\n\n# Calculate margins to center the overall pattern of n_cols_max by n_rows_max\nif n_cols_max > 0:\n    span_x = (n_cols_max - 1) * pitch_x + obround_hole_length\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0 \n    n_cols_max = 0 # Ensure it's 0 if no space\n\nif n_rows_max > 0:\n    span_y = (n_rows_max - 1) * pitch_y + obround_hole_width\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n    n_rows_max = 0 # Ensure it's 0 if no space\n\n\n# =========================\n# Create and cut holes from the plate\n# =========================\nresult_shape = base_plate_shape\n\nif n_cols_max > 0 and n_rows_max > 0:\n    for j in range(n_rows_max): # j is the row index (iterating in Y direction)\n        current_center_y = margin_y + obround_hole_width / 2.0 + j * pitch_y\n        \n        # Determine X stagger for this row j\n        # Alternate rows (e.g., odd j) are staggered by half of pitch_x\n        x_stagger_offset = 0.0\n        if j % 2 == 1: \n            x_stagger_offset = pitch_x / 2.0\n\n        for i in range(n_cols_max): # i is the column index (iterating in X direction)\n            current_center_x_base = margin_x + obround_hole_length / 2.0 + i * pitch_x\n            current_center_x = current_center_x_base + x_stagger_offset\n\n            # Boundary check: ensure the entire hole is within the plate\n            min_hole_x = current_center_x - obround_hole_length / 2.0\n            max_hole_x = current_center_x + obround_hole_length / 2.0\n            min_hole_y = current_center_y - obround_hole_width / 2.0\n            max_hole_y = current_center_y + obround_hole_width / 2.0\n            \n            tolerance = 1e-6 # Small tolerance for floating point comparisons\n\n            if (min_hole_x >= 0.0 - tolerance and \n                max_hole_x <= plate_length + tolerance and\n                min_hole_y >= 0.0 - tolerance and \n                max_hole_y <= plate_width + tolerance):\n                \n                hole_tool = make_single_obround_tool(current_center_x, current_center_y,\n                                                     obround_hole_length, obround_hole_width,\n                                                     plate_thickness)\n                result_shape = result_shape.cut(hole_tool)\n\n# =========================\n# Create the final Part Feature in the document\n# =========================\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedPlateStaggeredObrounds\") \nplate_obj.Label = sanitized_title \nplate_obj.Shape = result_shape\n\ndoc.recompute()\n\n# =========================\n# Export settings (absolute paths)\n# =========================\nscript_dir = os.path.dirname(os.path.abspath(__file__))\noutput_dir_abs = os.path.abspath(os.path.join(script_dir, \"..\", \"cad_outputs_generated\"))\nos.makedirs(output_dir_abs, exist_ok=True)\n\nstep_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\nobj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n#Example perforated sheet elips holes\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Perforated_sheet_with_elliptical_holes\"\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 300.0   # X dimension of the plate\nplate_width     = 200.0   # Y dimension of the plate\nplate_thickness = 2.0     # Z dimension (thickness)\n\nmajor_axis =  30.0  # Ellipse major axis (along X) \nminor_axis =  15.0  # Ellipse minor axis (along Y)\n\n# Pattern parameters – adjust as needed\nn_cols    = 10     # number of holes in X-direction\nn_rows    =  8     # number of holes in Y-direction\nspacing_x = 30.0   # center-to-center spacing in X\nspacing_y = 25.0   # center-to-center spacing in Y\n\n# ----------------------------------------\n# Compute margins to center the pattern\n# ----------------------------------------\nif n_cols > 0:\n    span_x   = (n_cols - 1) * spacing_x + major_axis\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif n_rows > 0:\n    span_y   = (n_rows - 1) * spacing_y + minor_axis\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Helper: make an extruded elliptical hole\n# ----------------------------------------\ndef make_elliptical_hole(cx, cy, maj, mino, thickness):\n    \"\"\"\n    Creates an extruded ellipse (solid) centered at (cx,cy) in XY,\n    extruded along Z by 'thickness'.\n    \"\"\"\n    # 1) create the 2D ellipse curve at origin\n    ell = Part.Ellipse()\n    ell.MajorRadius = maj / 2.0\n    ell.MinorRadius = mino / 2.0\n    edge = ell.toShape()\n    wire = Part.Wire([edge])\n    face = Part.Face(wire)\n    # 2) extrude face along Z\n    solid = face.extrude(App.Vector(0, 0, thickness))\n    # 3) translate to (cx, cy, 0)\n    solid.translate(App.Vector(cx, cy, 0.0))\n    return solid\n\n# ----------------------------------------\n# Generate all hole tools\n# ----------------------------------------\nhole_tools = []\ntol = 1e-6\nfor j in range(n_rows):\n    cy = margin_y + minor_axis / 2.0 + j * spacing_y\n    for i in range(n_cols):\n        cx = margin_x + major_axis / 2.0 + i * spacing_x\n        # boundary check\n        if (cx - major_axis/2.0 >= -tol and\n            cx + major_axis/2.0 <= plate_length + tol and\n            cy - minor_axis/2.0 >= -tol and\n            cy + minor_axis/2.0 <= plate_width + tol):\n            hole = make_elliptical_hole(cx, cy,\n                                       major_axis,\n                                       minor_axis,\n                                       plate_thickness)\n            hole_tools.append(hole)\n\n# ----------------------------------------\n# Perform boolean cut (all holes at once)\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    final_shape = base_plate.cut(compound_holes)\nelse:\n    final_shape = base_plate\n\n# ----------------------------------------\n# Create the final Part::Feature\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = \"Perforated_sheet_with_elliptical_holes\"\nplate_obj.Shape = final_shape\n\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ\n# ----------------------------------------\n# Determine base directory for exports (one level up from script)\nif \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    script_dir = os.getcwd()\n\noutput_dir = os.path.abspath(os.path.join(script_dir, \"..\", \"cad_outputs_generated\"))\nos.makedirs(output_dir, exist_ok=True)\n\nstep_path = os.path.join(output_dir, \"Perforated_sheet_with_elliptical_holes.step\")\nImport.export([plate_obj], step_path)\nprint(f\"Model exported to {step_path}\")\n\nobj_path = os.path.join(output_dir, \"Perforated_sheet_with_elliptical_holes.obj\")\nMesh.export([plate_obj], obj_path)\nprint(f\"Model exported to {obj_path}\")\n\n#Example perforated sheet elips stagger grid pattern\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_with_elliptical_holes\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm) – updated for user request\n# ----------------------------------------\nplate_length    = 1000.0   # X dimension of the sheet\nplate_width     = 500.0    # Y dimension of the sheet\nplate_thickness = 2.0      # Z dimension (thickness)\n\nmajor_axis = 30.0  # Ellipse major axis (along X)\nminor_axis = 15.0  # Ellipse minor axis (along Y)\n\n# Pattern parameters – reuse or adjust as needed\nn_cols    = 10     # number of holes in X-direction\nn_rows    = 8      # number of holes in Y-direction\nspacing_x = 80.0   # center-to-center spacing in X\nspacing_y = 50.0   # center-to-center spacing in Y\n\n# ----------------------------------------\n# Compute margins to center the pattern\n# ----------------------------------------\nif n_cols > 0:\n    span_x   = (n_cols - 1) * spacing_x + major_axis\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif n_rows > 0:\n    span_y   = (n_rows - 1) * spacing_y + minor_axis\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Helper: create an extruded elliptical hole\n# ----------------------------------------\ndef make_elliptical_hole(cx, cy, maj, mino, thickness):\n    \"\"\"\n    Returns a solid ellipse extruded along Z by 'thickness',\n    centered at (cx, cy).\n    \"\"\"\n    ell = Part.Ellipse()\n    ell.MajorRadius = maj / 2.0\n    ell.MinorRadius = mino / 2.0\n    edge = ell.toShape()\n    wire = Part.Wire([edge])\n    face = Part.Face(wire)\n    solid = face.extrude(App.Vector(0, 0, thickness))\n    solid.translate(App.Vector(cx, cy, 0.0))\n    return solid\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\ntol = 1e-6\nfor row in range(n_rows):\n    # Y position for this row\n    cy = margin_y + minor_axis / 2.0 + row * spacing_y\n    # Stagger offset: alternate rows shifted by half spacing_x\n    if row % 2 == 1:\n        x_stagger = spacing_x / 2.0\n    else:\n        x_stagger = 0.0\n    for col in range(n_cols):\n        # X position with stagger\n        cx = margin_x + major_axis / 2.0 + col * spacing_x + x_stagger\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - major_axis / 2.0\n        max_x = cx + major_axis / 2.0\n        min_y = cy - minor_axis / 2.0\n        max_y = cy + minor_axis / 2.0\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            hole = make_elliptical_hole(cx, cy, major_axis, minor_axis, plate_thickness)\n            hole_tools.append(hole)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\nif \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    script_dir = os.getcwd()\n\noutput_dir_abs = os.path.abspath(os.path.join(script_dir, \"..\", \"cad_outputs_generated\"))\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(output_dir_abs, f\"{sanitized_title}.step\")\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(output_dir_abs, f\"{sanitized_title}.obj\")\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n\n#Example perforated sheet R12 T16 1x500x1000\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_R12_T16\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 500.0   # X dimension of the sheet\nplate_width     = 1000.0   # Y dimension of the sheet\nplate_thickness = 1.0     # Z dimension (thickness)\n\nhole_diameter = 12.0      # Hole diameter Ø12 mm\nhole_radius   = hole_diameter / 2.0\n\npitch_x = 16.0            # center-to-center spacing in X\npitch_y = 16.0            # center-to-center spacing in Y\n\ntol = 1e-6                # tolerance for boundary checks\n\n# ----------------------------------------\n# Compute number of holes and margins\n# ----------------------------------------\nif plate_length < hole_diameter:\n    n_cols = 0\nelse:\n    n_cols = int((plate_length - hole_diameter) / pitch_x) + 1\nif n_cols > 0:\n    span_x = (n_cols - 1) * pitch_x + hole_diameter\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif plate_width < hole_diameter:\n    n_rows = 0\nelse:\n    n_rows = int((plate_width - hole_diameter) / pitch_y) + 1\nif n_rows > 0:\n    span_y = (n_rows - 1) * pitch_y + hole_diameter\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\nfor j in range(n_rows):\n    # Y position for this row\n    cy = margin_y + hole_radius + j * pitch_y\n    # Stagger offset: alternate rows shifted by half pitch_x\n    if j % 2 == 1:\n        x_stagger = pitch_x / 2.0\n    else:\n        x_stagger = 0.0\n    for i in range(n_cols):\n        # X position with stagger\n        cx = margin_x + hole_radius + i * pitch_x + x_stagger\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - hole_radius\n        max_x = cx + hole_radius\n        min_y = cy - hole_radius\n        max_y = cy + hole_radius\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            cyl = Part.makeCylinder(\n                hole_radius,\n                plate_thickness,\n                App.Vector(cx, cy, 0.0),\n                App.Vector(0.0, 0.0, 1.0)\n            )\n            hole_tools.append(cyl)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\n\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\noutput_dir_abs = os.path.abspath(\n    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')\n)\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.step\"\n)\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.obj\"\n)\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n#Example perforated sheet R0.54 T1 1x500x1000\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_R12_T16\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 500.0   # X dimension of the sheet\nplate_width     = 1000.0   # Y dimension of the sheet\nplate_thickness = 1.0     # Z dimension (thickness)\n\nhole_diameter = 0.54     # Hole diameter Ø12 mm\nhole_radius   = hole_diameter / 2.0\n\npitch_x = 1.0            # center-to-center spacing in X\npitch_y = 1.0            # center-to-center spacing in Y\n\ntol = 1e-6                # tolerance for boundary checks\n\n# ----------------------------------------\n# Compute number of holes and margins\n# ----------------------------------------\nif plate_length < hole_diameter:\n    n_cols = 0\nelse:\n    n_cols = int((plate_length - hole_diameter) / pitch_x) + 1\nif n_cols > 0:\n    span_x = (n_cols - 1) * pitch_x + hole_diameter\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif plate_width < hole_diameter:\n    n_rows = 0\nelse:\n    n_rows = int((plate_width - hole_diameter) / pitch_y) + 1\nif n_rows > 0:\n    span_y = (n_rows - 1) * pitch_y + hole_diameter\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\nfor j in range(n_rows):\n    # Y position for this row\n    cy = margin_y + hole_radius + j * pitch_y\n    # Stagger offset: alternate rows shifted by half pitch_x\n    if j % 2 == 1:\n        x_stagger = pitch_x / 2.0\n    else:\n        x_stagger = 0.0\n    for i in range(n_cols):\n        # X position with stagger\n        cx = margin_x + hole_radius + i * pitch_x + x_stagger\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - hole_radius\n        max_x = cx + hole_radius\n        min_y = cy - hole_radius\n        max_y = cy + hole_radius\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            cyl = Part.makeCylinder(\n                hole_radius,\n                plate_thickness,\n                App.Vector(cx, cy, 0.0),\n                App.Vector(0.0, 0.0, 1.0)\n            )\n            hole_tools.append(cyl)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\n\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\noutput_dir_abs = os.path.abspath(\n    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')\n)\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.step\"\n)\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.obj\"\n)\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n#Emxample perforated sheet R0.57 T1 20x15x1\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_R0.57_T1_20x15x1\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 20.0    # X dimension of the sheet\nplate_width     = 15.0    # Y dimension of the sheet\nplate_thickness = 1.0     # Z dimension (thickness)\n\nhole_diameter   = 0.57    # Diameter of each round hole\nhole_radius     = hole_diameter / 2.0\n\nspacing_x       = 1.0     # center‐to‐center spacing in X\nspacing_y       = 1.0     # center‐to‐center spacing in Y\n\ntol = 1e-6                # tolerance for boundary checks\n\n# ----------------------------------------\n# Compute number of holes and margins\n# ----------------------------------------\n# Columns\nif plate_length < hole_diameter:\n    n_cols = 0\nelse:\n    n_cols = int((plate_length - hole_diameter) / spacing_x) + 1\n\nif n_cols > 0:\n    span_x   = (n_cols - 1) * spacing_x + hole_diameter\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\n# Rows\nif plate_width < hole_diameter:\n    n_rows = 0\nelse:\n    n_rows = int((plate_width - hole_diameter) / spacing_y) + 1\n\nif n_rows > 0:\n    span_y   = (n_rows - 1) * spacing_y + hole_diameter\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\nfor row in range(n_rows):\n    # Y position for this row\n    cy = margin_y + hole_radius + row * spacing_y\n\n    # Stagger offset: every other row shifted by half spacing_x\n    if row % 2 == 1:\n        x_stagger = spacing_x / 2.0\n    else:\n        x_stagger = 0.0\n\n    for col in range(n_cols):\n        # X position with optional stagger\n        cx = margin_x + hole_radius + col * spacing_x + x_stagger\n\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - hole_radius\n        max_x = cx + hole_radius\n        min_y = cy - hole_radius\n        max_y = cy + hole_radius\n\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            cyl = Part.makeCylinder(\n                hole_radius,\n                plate_thickness,\n                App.Vector(cx, cy, 0.0),\n                App.Vector(0.0, 0.0, 1.0)\n            )\n            hole_tools.append(cyl)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\n# Determine script directory robustly\nif \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    script_dir = os.getcwd()\n\noutput_dir_abs = os.path.abspath(\n    os.path.join(script_dir, \"..\", \"cad_outputs_generated\")\n)\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.step\"\n)\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.obj\"\n)\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")", "script_name": "Rectangular with 4 corner holes and chamfered edges", "script_content": "import FreeCAD as App\nimport Part\nimport os\nimport Import\n\n# Create a new, headless FreeCAD document\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_box_with_4_corner_holes_and_chamfered_edges\"\n\n# -----------------------------\n# Parameters (for easy tuning)\n# -----------------------------\nlength       = 100.0   # X dimension of the box\nwidth        = 60.0    # Y dimension of the box\nheight       = 20.0    # Z dimension of the box\nhole_radius  = 5.0     # Radius of each corner hole\nchamfer_size = 2.0     # Size of the chamfer on all external edges\n\n# --------------------------------------\n# 1) Create the base rectangular box\n# --------------------------------------\nbase_box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))\n\n# --------------------------------------\n# 2) Chamfer all external edges of box\n# --------------------------------------\n# Get all edges of the box\nedges = base_box_shape.Edges\n# Apply chamfer using a single radius for all edges\nchamfered_box = base_box_shape.makeChamfer(chamfer_size, edges)\n\n# --------------------------------------\n# 3) Define corner-hole cylinder positions\n# --------------------------------------\nhole_positions = [\n    App.Vector(10.0, 10.0, 0.0),\n    App.Vector(length - 10.0, 10.0, 0.0),\n    App.Vector(10.0, width  - 10.0, 0.0),\n    App.Vector(length - 10.0, width  - 10.0, 0.0),\n]\n\n# --------------------------------------\n# 4) Subtract four through-holes\n# --------------------------------------\nresult_shape = chamfered_box\nfor idx, pos in enumerate(hole_positions, start=1):\n    cyl = Part.makeCylinder(hole_radius, height, pos, App.Vector(0.0, 0.0, 1.0))\n    result_shape = result_shape.cut(cyl)\n\n# --------------------------------------\n# 5) Create the final Part::Feature object\n# --------------------------------------\nfinal_obj = doc.addObject(\"Part::Feature\",\n                          \"Rectangular_Box_With_4_Corner_Holes_and_Chamfered_Edges\")\nfinal_obj.Shape = result_shape\n\n# Recompute document to finalize all operations\ndoc.recompute()\n\n# --------------------------------------\n# 6) Export the final shape to STEP\n# --------------------------------------\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(\n    output_dir,\n    \"Rectangular_box_with_4_corner_holes_and_chamfered_edges.step\"\n)\nImport.export([final_obj], step_path)\n\n# --------------------------------------\n# Script complete: the STEP file is ready\n# --------------------------------------\n\n#Example rectangular with chamfered edges\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\n\n# -----------------------------\n# Parameters (for easy tuning)\n# -----------------------------\nlength       = 100.0   # X dimension of the box\nwidth        = 50.0    # Y dimension of the box\nheight       = 20.0    # Z dimension of the box\nchamfer_size = 2.0     # Chamfer size on all external edges (2 mm at 45°)\n\n# -----------------------------\n# 1) Create a new, headless document\n# -----------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Rectangular_block_with_chamfered_edges\"\n\n# -----------------------------\n# 2) Create the base rectangular box\n# -----------------------------\nbase_box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))\nbase_box_obj   = doc.addObject(\"Part::Feature\", \"BaseBox\")\nbase_box_obj.Shape = base_box_shape\n\n# -----------------------------\n# 3) Apply chamfer to all external edges\n# -----------------------------\n# Collect all edges of the box\nedges = base_box_shape.Edges\n\n# Perform chamfer operation\nchamfered_shape = base_box_shape.makeChamfer(chamfer_size, edges)\n\n# Create a new feature for the chamfered box\nchamfered_box_obj = doc.addObject(\"Part::Feature\", \"ChamferedBox\")\nchamfered_box_obj.Shape = chamfered_shape\n\n# -----------------------------\n# 4) Finalize the model\n# -----------------------------\ndoc.recompute()\n\n# -----------------------------\n# 5) Export to STEP\n# -----------------------------\noutput_dir = \"cad_outputs\"\nif not os.path.exists(output_dir):\n    os.makedirs(output_dir)\nstep_path = os.path.join(output_dir, \"Rectangular_block_with_chamfered_edges.step\")\nImport.export([chamfered_box_obj], step_path)\n\n# Final recompute to ensure integrity\ndoc.recompute()\n\n#Example Perforlated sheet C20_U40_1200x600\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport Import\nimport Mesh\nimport os\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"C20_U40_1200x600_Optimized\"  # DO NOT CHANGE THIS VALUE (Added _Optimized for clarity)\n\n# =========================\n# Document Setup\n# =========================\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# =========================\n# Parameters (mm)\n# =========================\nlength = 1200.0  # X dimension (length)\nwidth  =  600.0  # Y dimension (width)\nheight =    2.0  # Z dimension (thickness)\n\n# =========================\n# Create the metal plate\n# =========================\nbase_plate = Part.makeBox(length,\n                          width,\n                          height,\n                          App.Vector(0.0, 0.0, 0.0))\n\n# =========================\n# Drill square holes (evenly distribute holes with equal margins on both edges)\n#   - square side: 20 mm\n#   - center spacing: 40 mm in X and Y\n# =========================\nhole_side = 20.0\nspacing   = 40.0\ntool_height = height * 1.5 # Make tool slightly thicker for robust cutting\n\n# Compute number of holes in X direction\nif length < hole_side:\n    n_x = 0\nelse:\n    n_x = int((length - hole_side) // spacing) + 1\n\n# Total span occupied by holes\nif n_x > 0:\n    span_x = (n_x - 1) * spacing + hole_side\n    # Margin on each side to center the holes\n    margin_x = (length - span_x) / 2.0\nelse:\n    margin_x = length / 2.0 # Or handle as no holes\n\n# Build X centers array\nx_centers = []\nif n_x > 0:\n    x_centers = [\n        margin_x + hole_side / 2.0 + i * spacing\n        for i in range(n_x)\n    ]\n\n# Compute number of holes in Y direction\nif width < hole_side:\n    n_y = 0\nelse:\n    n_y = int((width - hole_side) // spacing) + 1\n\nif n_y > 0:\n    span_y = (n_y - 1) * spacing + hole_side\n    margin_y = (width - span_y) / 2.0\nelse:\n    margin_y = width / 2.0 # Or handle as no holes\n\n# Build Y centers array\ny_centers = []\nif n_y > 0:\n    y_centers = [\n        margin_y + hole_side / 2.0 + j * spacing\n        for j in range(n_y)\n    ]\n\n# Start with the base plate shape\nresult_shape = base_plate\nall_hole_tools = [] # List to store all hole cutting tools\n\n# For each hole center, create a square prism and add to list\nif n_x > 0 and n_y > 0:\n    for cx in x_centers:\n        for cy in y_centers:\n            # Boundary check (optional, as centering logic should handle it, but good for robustness)\n            # Ensure the entire hole is within the plate boundaries\n            min_hole_x = cx - hole_side / 2.0\n            max_hole_x = cx + hole_side / 2.0\n            min_hole_y = cy - hole_side / 2.0\n            max_hole_y = cy + hole_side / 2.0\n            tolerance = 1e-6\n\n            if (min_hole_x >= 0.0 - tolerance and\n                max_hole_x <= length + tolerance and\n                min_hole_y >= 0.0 - tolerance and\n                max_hole_y <= width + tolerance):\n\n                hole_corner = App.Vector(cx - hole_side / 2.0,\n                                         cy - hole_side / 2.0,\n                                         (height - tool_height) / 2.0) # Center tool vertically for good measure\n                hole_box = Part.makeBox(hole_side,\n                                        hole_side,\n                                        tool_height, # Use slightly thicker tool\n                                        hole_corner)\n                all_hole_tools.append(hole_box)\n\n# If there are tools, create a compound and cut once\nif all_hole_tools:\n    holes_compound = Part.Compound(all_hole_tools)\n    result_shape = base_plate.cut(holes_compound)\n\n# =========================\n# Create the final Part Feature\n# =========================\nplate_obj = doc.addObject(\"Part::Feature\", \"MetalPlateWithHoles\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\n\n# Recompute to finalize geometry\ndoc.recompute()\n\n# =========================\n# Export settings (absolute paths)\n# =========================\n# Determine script directory robustly\nif \"__file__\" in locals() or \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    # Fallback for environments where __file__ is not defined (e.g. FreeCAD GUI Python console)\n    script_dir = os.getcwd() \n\noutput_dir_abs = os.path.abspath(os.path.join(script_dir,\n                                              '..',\n                                              'cad_outputs_generated'))\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(output_dir_abs,\n                                 f'{sanitized_title}.step')\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(output_dir_abs,\n                                f'{sanitized_title}.obj')\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\nprint(f\"Generated {sanitized_title}\")\n\n\n#Example perforated sheet LR5x20 Z9x24 600x300\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"LR5x20_Z9x24_600x300\"  # DO NOT CHANGE THIS VALUE\n\n# =========================\n# Document Setup\n# =========================\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# =========================\n# Parameters (mm) for the plate\n# =========================\nplate_length = 600.0  # X dimension\nplate_width  = 300.0  # Y dimension\nplate_thickness = 2.0  # Z dimension\n\n# =========================\n# Create the base metal plate\n# =========================\nbase_plate_shape = Part.makeBox(plate_length,\n                                plate_width,\n                                plate_thickness,\n                                App.Vector(0.0, 0.0, 0.0))\n\n# =========================\n# Parameters for the obround holes (e.g., LR5x20 type)\n# =========================\n# \"Horizontally oriented\" means length of obround is along plate's X-axis.\nobround_hole_length = 20.0  # Total length of the obround (along plate X)\nobround_hole_width  = 5.0   # Total width of the obround (along plate Y)\n\n# =========================\n# Parameters for the staggered pattern\n# =========================\n# \"horizontal spacing between adjacent holes\" -> pitch_x (center-to-center in X for non-staggered elements)\n# \"vertical offset between rows\" -> pitch_y (center-to-center in Y between rows of holes)\n# These pitch values can be adjusted to change pattern density.\npitch_x = 24.0  # Center-to-center spacing in X-direction for holes in the same conceptual line\npitch_y = 9.0  # Center-to-center spacing in Y-direction between rows of holes\n\n# =========================\n# Helper function to create a single obround tool\n# Creates an obround in the XY plane, centered at (cx, cy), extruded by 'thickness_z' along Z.\n# total_len is aligned with the X-axis, obround_h with the Y-axis.\n# =========================\ndef make_single_obround_tool(cx, cy, total_len, obround_h, thickness_z):\n    radius = obround_h / 2.0\n    # Length of the central rectangular part of the obround\n    rect_len = total_len - 2 * radius \n\n    if rect_len <= 1e-6: # If effectively a circle (e.g. total_len == obround_h)\n        tool_shape = Part.makeCylinder(radius, thickness_z, \n                                       App.Vector(cx, cy, 0), \n                                       App.Vector(0,0,1)) # Axis along Z\n    else:\n        # Central rectangular prism\n        rect_base_pos = App.Vector(cx - rect_len / 2.0,\n                                   cy - obround_h / 2.0,\n                                   0.0)\n        rect_box = Part.makeBox(rect_len, obround_h, thickness_z, rect_base_pos)\n\n        # Cylinder at the \"positive X\" end of the rectangle\n        cyl1_center_pos = App.Vector(cx + rect_len / 2.0, cy, 0.0)\n        cyl1 = Part.makeCylinder(radius, thickness_z, cyl1_center_pos, App.Vector(0,0,1))\n\n        # Cylinder at the \"negative X\" end of the rectangle\n        cyl2_center_pos = App.Vector(cx - rect_len / 2.0, cy, 0.0)\n        cyl2 = Part.makeCylinder(radius, thickness_z, cyl2_center_pos, App.Vector(0,0,1))\n        \n        tool_shape = rect_box.fuse(cyl1).fuse(cyl2)\n    return tool_shape\n\n# =========================\n# Calculate hole distribution: number of rows/columns and margins\n# =========================\n\n# Max number of holes in X direction (columns of holes) if not staggered\nif plate_length < obround_hole_length:\n    n_cols_max = 0\nelse:\n    n_cols_max = int((plate_length - obround_hole_length) / pitch_x) + 1\n    \n# Max number of holes in Y direction (rows of holes)\nif plate_width < obround_hole_width:\n    n_rows_max = 0\nelse:\n    n_rows_max = int((plate_width - obround_hole_width) / pitch_y) + 1\n\n# Calculate margins to center the overall pattern of n_cols_max by n_rows_max\nif n_cols_max > 0:\n    span_x = (n_cols_max - 1) * pitch_x + obround_hole_length\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0 \n    n_cols_max = 0 # Ensure it's 0 if no space\n\nif n_rows_max > 0:\n    span_y = (n_rows_max - 1) * pitch_y + obround_hole_width\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n    n_rows_max = 0 # Ensure it's 0 if no space\n\n\n# =========================\n# Create and cut holes from the plate\n# =========================\nresult_shape = base_plate_shape\n\nif n_cols_max > 0 and n_rows_max > 0:\n    for j in range(n_rows_max): # j is the row index (iterating in Y direction)\n        current_center_y = margin_y + obround_hole_width / 2.0 + j * pitch_y\n        \n        # Determine X stagger for this row j\n        # Alternate rows (e.g., odd j) are staggered by half of pitch_x\n        x_stagger_offset = 0.0\n        if j % 2 == 1: \n            x_stagger_offset = pitch_x / 2.0\n\n        for i in range(n_cols_max): # i is the column index (iterating in X direction)\n            current_center_x_base = margin_x + obround_hole_length / 2.0 + i * pitch_x\n            current_center_x = current_center_x_base + x_stagger_offset\n\n            # Boundary check: ensure the entire hole is within the plate\n            min_hole_x = current_center_x - obround_hole_length / 2.0\n            max_hole_x = current_center_x + obround_hole_length / 2.0\n            min_hole_y = current_center_y - obround_hole_width / 2.0\n            max_hole_y = current_center_y + obround_hole_width / 2.0\n            \n            tolerance = 1e-6 # Small tolerance for floating point comparisons\n\n            if (min_hole_x >= 0.0 - tolerance and \n                max_hole_x <= plate_length + tolerance and\n                min_hole_y >= 0.0 - tolerance and \n                max_hole_y <= plate_width + tolerance):\n                \n                hole_tool = make_single_obround_tool(current_center_x, current_center_y,\n                                                     obround_hole_length, obround_hole_width,\n                                                     plate_thickness)\n                result_shape = result_shape.cut(hole_tool)\n\n# =========================\n# Create the final Part Feature in the document\n# =========================\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedPlateStaggeredObrounds\") \nplate_obj.Label = sanitized_title \nplate_obj.Shape = result_shape\n\ndoc.recompute()\n\n# =========================\n# Export settings (absolute paths)\n# =========================\nscript_dir = os.path.dirname(os.path.abspath(__file__))\noutput_dir_abs = os.path.abspath(os.path.join(script_dir, \"..\", \"cad_outputs_generated\"))\nos.makedirs(output_dir_abs, exist_ok=True)\n\nstep_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\nobj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n#Example perforated sheet elips holes\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = \"Perforated_sheet_with_elliptical_holes\"\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 300.0   # X dimension of the plate\nplate_width     = 200.0   # Y dimension of the plate\nplate_thickness = 2.0     # Z dimension (thickness)\n\nmajor_axis =  30.0  # Ellipse major axis (along X) \nminor_axis =  15.0  # Ellipse minor axis (along Y)\n\n# Pattern parameters – adjust as needed\nn_cols    = 10     # number of holes in X-direction\nn_rows    =  8     # number of holes in Y-direction\nspacing_x = 30.0   # center-to-center spacing in X\nspacing_y = 25.0   # center-to-center spacing in Y\n\n# ----------------------------------------\n# Compute margins to center the pattern\n# ----------------------------------------\nif n_cols > 0:\n    span_x   = (n_cols - 1) * spacing_x + major_axis\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif n_rows > 0:\n    span_y   = (n_rows - 1) * spacing_y + minor_axis\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Helper: make an extruded elliptical hole\n# ----------------------------------------\ndef make_elliptical_hole(cx, cy, maj, mino, thickness):\n    \"\"\"\n    Creates an extruded ellipse (solid) centered at (cx,cy) in XY,\n    extruded along Z by 'thickness'.\n    \"\"\"\n    # 1) create the 2D ellipse curve at origin\n    ell = Part.Ellipse()\n    ell.MajorRadius = maj / 2.0\n    ell.MinorRadius = mino / 2.0\n    edge = ell.toShape()\n    wire = Part.Wire([edge])\n    face = Part.Face(wire)\n    # 2) extrude face along Z\n    solid = face.extrude(App.Vector(0, 0, thickness))\n    # 3) translate to (cx, cy, 0)\n    solid.translate(App.Vector(cx, cy, 0.0))\n    return solid\n\n# ----------------------------------------\n# Generate all hole tools\n# ----------------------------------------\nhole_tools = []\ntol = 1e-6\nfor j in range(n_rows):\n    cy = margin_y + minor_axis / 2.0 + j * spacing_y\n    for i in range(n_cols):\n        cx = margin_x + major_axis / 2.0 + i * spacing_x\n        # boundary check\n        if (cx - major_axis/2.0 >= -tol and\n            cx + major_axis/2.0 <= plate_length + tol and\n            cy - minor_axis/2.0 >= -tol and\n            cy + minor_axis/2.0 <= plate_width + tol):\n            hole = make_elliptical_hole(cx, cy,\n                                       major_axis,\n                                       minor_axis,\n                                       plate_thickness)\n            hole_tools.append(hole)\n\n# ----------------------------------------\n# Perform boolean cut (all holes at once)\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    final_shape = base_plate.cut(compound_holes)\nelse:\n    final_shape = base_plate\n\n# ----------------------------------------\n# Create the final Part::Feature\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = \"Perforated_sheet_with_elliptical_holes\"\nplate_obj.Shape = final_shape\n\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ\n# ----------------------------------------\n# Determine base directory for exports (one level up from script)\nif \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    script_dir = os.getcwd()\n\noutput_dir = os.path.abspath(os.path.join(script_dir, \"..\", \"cad_outputs_generated\"))\nos.makedirs(output_dir, exist_ok=True)\n\nstep_path = os.path.join(output_dir, \"Perforated_sheet_with_elliptical_holes.step\")\nImport.export([plate_obj], step_path)\nprint(f\"Model exported to {step_path}\")\n\nobj_path = os.path.join(output_dir, \"Perforated_sheet_with_elliptical_holes.obj\")\nMesh.export([plate_obj], obj_path)\nprint(f\"Model exported to {obj_path}\")\n\n#Example perforated sheet elips stagger grid pattern\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_with_elliptical_holes\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm) – updated for user request\n# ----------------------------------------\nplate_length    = 1000.0   # X dimension of the sheet\nplate_width     = 500.0    # Y dimension of the sheet\nplate_thickness = 2.0      # Z dimension (thickness)\n\nmajor_axis = 30.0  # Ellipse major axis (along X)\nminor_axis = 15.0  # Ellipse minor axis (along Y)\n\n# Pattern parameters – reuse or adjust as needed\nn_cols    = 10     # number of holes in X-direction\nn_rows    = 8      # number of holes in Y-direction\nspacing_x = 80.0   # center-to-center spacing in X\nspacing_y = 50.0   # center-to-center spacing in Y\n\n# ----------------------------------------\n# Compute margins to center the pattern\n# ----------------------------------------\nif n_cols > 0:\n    span_x   = (n_cols - 1) * spacing_x + major_axis\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif n_rows > 0:\n    span_y   = (n_rows - 1) * spacing_y + minor_axis\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Helper: create an extruded elliptical hole\n# ----------------------------------------\ndef make_elliptical_hole(cx, cy, maj, mino, thickness):\n    \"\"\"\n    Returns a solid ellipse extruded along Z by 'thickness',\n    centered at (cx, cy).\n    \"\"\"\n    ell = Part.Ellipse()\n    ell.MajorRadius = maj / 2.0\n    ell.MinorRadius = mino / 2.0\n    edge = ell.toShape()\n    wire = Part.Wire([edge])\n    face = Part.Face(wire)\n    solid = face.extrude(App.Vector(0, 0, thickness))\n    solid.translate(App.Vector(cx, cy, 0.0))\n    return solid\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\ntol = 1e-6\nfor row in range(n_rows):\n    # Y position for this row\n    cy = margin_y + minor_axis / 2.0 + row * spacing_y\n    # Stagger offset: alternate rows shifted by half spacing_x\n    if row % 2 == 1:\n        x_stagger = spacing_x / 2.0\n    else:\n        x_stagger = 0.0\n    for col in range(n_cols):\n        # X position with stagger\n        cx = margin_x + major_axis / 2.0 + col * spacing_x + x_stagger\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - major_axis / 2.0\n        max_x = cx + major_axis / 2.0\n        min_y = cy - minor_axis / 2.0\n        max_y = cy + minor_axis / 2.0\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            hole = make_elliptical_hole(cx, cy, major_axis, minor_axis, plate_thickness)\n            hole_tools.append(hole)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\nif \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    script_dir = os.getcwd()\n\noutput_dir_abs = os.path.abspath(os.path.join(script_dir, \"..\", \"cad_outputs_generated\"))\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(output_dir_abs, f\"{sanitized_title}.step\")\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(output_dir_abs, f\"{sanitized_title}.obj\")\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n\n#Example perforated sheet R12 T16 1x500x1000\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_R12_T16\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 500.0   # X dimension of the sheet\nplate_width     = 1000.0   # Y dimension of the sheet\nplate_thickness = 1.0     # Z dimension (thickness)\n\nhole_diameter = 12.0      # Hole diameter Ø12 mm\nhole_radius   = hole_diameter / 2.0\n\npitch_x = 16.0            # center-to-center spacing in X\npitch_y = 16.0            # center-to-center spacing in Y\n\ntol = 1e-6                # tolerance for boundary checks\n\n# ----------------------------------------\n# Compute number of holes and margins\n# ----------------------------------------\nif plate_length < hole_diameter:\n    n_cols = 0\nelse:\n    n_cols = int((plate_length - hole_diameter) / pitch_x) + 1\nif n_cols > 0:\n    span_x = (n_cols - 1) * pitch_x + hole_diameter\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif plate_width < hole_diameter:\n    n_rows = 0\nelse:\n    n_rows = int((plate_width - hole_diameter) / pitch_y) + 1\nif n_rows > 0:\n    span_y = (n_rows - 1) * pitch_y + hole_diameter\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\nfor j in range(n_rows):\n    # Y position for this row\n    cy = margin_y + hole_radius + j * pitch_y\n    # Stagger offset: alternate rows shifted by half pitch_x\n    if j % 2 == 1:\n        x_stagger = pitch_x / 2.0\n    else:\n        x_stagger = 0.0\n    for i in range(n_cols):\n        # X position with stagger\n        cx = margin_x + hole_radius + i * pitch_x + x_stagger\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - hole_radius\n        max_x = cx + hole_radius\n        min_y = cy - hole_radius\n        max_y = cy + hole_radius\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            cyl = Part.makeCylinder(\n                hole_radius,\n                plate_thickness,\n                App.Vector(cx, cy, 0.0),\n                App.Vector(0.0, 0.0, 1.0)\n            )\n            hole_tools.append(cyl)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\n\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\noutput_dir_abs = os.path.abspath(\n    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')\n)\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.step\"\n)\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.obj\"\n)\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n#Example perforated sheet R0.54 T1 1x500x1000\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_R12_T16\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 500.0   # X dimension of the sheet\nplate_width     = 1000.0   # Y dimension of the sheet\nplate_thickness = 1.0     # Z dimension (thickness)\n\nhole_diameter = 0.54     # Hole diameter Ø12 mm\nhole_radius   = hole_diameter / 2.0\n\npitch_x = 1.0            # center-to-center spacing in X\npitch_y = 1.0            # center-to-center spacing in Y\n\ntol = 1e-6                # tolerance for boundary checks\n\n# ----------------------------------------\n# Compute number of holes and margins\n# ----------------------------------------\nif plate_length < hole_diameter:\n    n_cols = 0\nelse:\n    n_cols = int((plate_length - hole_diameter) / pitch_x) + 1\nif n_cols > 0:\n    span_x = (n_cols - 1) * pitch_x + hole_diameter\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\nif plate_width < hole_diameter:\n    n_rows = 0\nelse:\n    n_rows = int((plate_width - hole_diameter) / pitch_y) + 1\nif n_rows > 0:\n    span_y = (n_rows - 1) * pitch_y + hole_diameter\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\nfor j in range(n_rows):\n    # Y position for this row\n    cy = margin_y + hole_radius + j * pitch_y\n    # Stagger offset: alternate rows shifted by half pitch_x\n    if j % 2 == 1:\n        x_stagger = pitch_x / 2.0\n    else:\n        x_stagger = 0.0\n    for i in range(n_cols):\n        # X position with stagger\n        cx = margin_x + hole_radius + i * pitch_x + x_stagger\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - hole_radius\n        max_x = cx + hole_radius\n        min_y = cy - hole_radius\n        max_y = cy + hole_radius\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            cyl = Part.makeCylinder(\n                hole_radius,\n                plate_thickness,\n                App.Vector(cx, cy, 0.0),\n                App.Vector(0.0, 0.0, 1.0)\n            )\n            hole_tools.append(cyl)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\n\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\noutput_dir_abs = os.path.abspath(\n    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')\n)\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.step\"\n)\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.obj\"\n)\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")\n\n#Emxample perforated sheet R0.57 T1 20x15x1\n#!/usr/bin/env python3\nimport FreeCAD as App\nimport Part\nimport os\nimport Import\nimport Mesh\n\n# Define the sanitized_title variable with the exact value provided to this template\nsanitized_title = \"Perforated_sheet_R0.57_T1_20x15x1\"  # DO NOT CHANGE THIS VALUE\n\n# ----------------------------------------\n# Document Setup\n# ----------------------------------------\ndoc = App.newDocument(\"GeneratedModel\")\ndoc.Label = sanitized_title\n\n# ----------------------------------------\n# Parameters (mm)\n# ----------------------------------------\nplate_length    = 20.0    # X dimension of the sheet\nplate_width     = 15.0    # Y dimension of the sheet\nplate_thickness = 1.0     # Z dimension (thickness)\n\nhole_diameter   = 0.57    # Diameter of each round hole\nhole_radius     = hole_diameter / 2.0\n\nspacing_x       = 1.0     # center‐to‐center spacing in X\nspacing_y       = 1.0     # center‐to‐center spacing in Y\n\ntol = 1e-6                # tolerance for boundary checks\n\n# ----------------------------------------\n# Compute number of holes and margins\n# ----------------------------------------\n# Columns\nif plate_length < hole_diameter:\n    n_cols = 0\nelse:\n    n_cols = int((plate_length - hole_diameter) / spacing_x) + 1\n\nif n_cols > 0:\n    span_x   = (n_cols - 1) * spacing_x + hole_diameter\n    margin_x = (plate_length - span_x) / 2.0\nelse:\n    margin_x = plate_length / 2.0\n\n# Rows\nif plate_width < hole_diameter:\n    n_rows = 0\nelse:\n    n_rows = int((plate_width - hole_diameter) / spacing_y) + 1\n\nif n_rows > 0:\n    span_y   = (n_rows - 1) * spacing_y + hole_diameter\n    margin_y = (plate_width - span_y) / 2.0\nelse:\n    margin_y = plate_width / 2.0\n\n# ----------------------------------------\n# Create the base plate\n# ----------------------------------------\nbase_plate = Part.makeBox(\n    plate_length,\n    plate_width,\n    plate_thickness,\n    App.Vector(0.0, 0.0, 0.0)\n)\n\n# ----------------------------------------\n# Build all hole tools in a staggered grid pattern\n# ----------------------------------------\nhole_tools = []\nfor row in range(n_rows):\n    # Y position for this row\n    cy = margin_y + hole_radius + row * spacing_y\n\n    # Stagger offset: every other row shifted by half spacing_x\n    if row % 2 == 1:\n        x_stagger = spacing_x / 2.0\n    else:\n        x_stagger = 0.0\n\n    for col in range(n_cols):\n        # X position with optional stagger\n        cx = margin_x + hole_radius + col * spacing_x + x_stagger\n\n        # Boundary check to ensure hole fully on plate\n        min_x = cx - hole_radius\n        max_x = cx + hole_radius\n        min_y = cy - hole_radius\n        max_y = cy + hole_radius\n\n        if (min_x >= -tol and max_x <= plate_length + tol and\n            min_y >= -tol and max_y <= plate_width + tol):\n            cyl = Part.makeCylinder(\n                hole_radius,\n                plate_thickness,\n                App.Vector(cx, cy, 0.0),\n                App.Vector(0.0, 0.0, 1.0)\n            )\n            hole_tools.append(cyl)\n\n# ----------------------------------------\n# Boolean cut: subtract all holes at once\n# ----------------------------------------\nif hole_tools:\n    compound_holes = Part.makeCompound(hole_tools)\n    result_shape = base_plate.cut(compound_holes)\nelse:\n    result_shape = base_plate\n\n# ----------------------------------------\n# Create and register final object\n# ----------------------------------------\nplate_obj = doc.addObject(\"Part::Feature\", \"PerforatedSheet\")\nplate_obj.Label = sanitized_title\nplate_obj.Shape = result_shape\ndoc.recompute()\n\n# ----------------------------------------\n# Export to STEP and OBJ using absolute paths\n# ----------------------------------------\n# Determine script directory robustly\nif \"__file__\" in globals():\n    script_dir = os.path.dirname(os.path.abspath(__file__))\nelse:\n    script_dir = os.getcwd()\n\noutput_dir_abs = os.path.abspath(\n    os.path.join(script_dir, \"..\", \"cad_outputs_generated\")\n)\nos.makedirs(output_dir_abs, exist_ok=True)\n\n# STEP export\nstep_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.step\"\n)\nImport.export([plate_obj], step_filename_abs)\nprint(f\"Model exported to {step_filename_abs}\")\n\n# OBJ export\nobj_filename_abs = os.path.join(\n    output_dir_abs, f\"{sanitized_title}.obj\"\n)\nMesh.export([plate_obj], obj_filename_abs)\nprint(f\"Model exported to {obj_filename_abs}\")", "source": "data/example.txt"}]