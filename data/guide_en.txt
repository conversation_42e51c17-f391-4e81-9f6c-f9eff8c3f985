Python Commands for 3D Drawing in FreeCAD Workbenches
Below is a detailed list of Python commands from the main FreeCAD workbenches that support drawing and manipulating 3D shapes, along with examples:

**1. Part Workbench (Core for 3D Modeling)**
*Basic Objects:*
- `Part.makeBox(length, width, height, [point])` - Creates a box, origin point can be specified
  *Example:* `box = Part.makeBox(10, 20, 5)`
- `Part.makeCylinder(radius, height, [point], [direction])` - Creates a cylinder with radius and height
  *Example:* `cylinder = Part.makeCylinder(5, 30)`
- `Part.makeSphere(radius, [point])` - Creates a sphere with radius
  *Example:* `sphere = Part.makeSphere(10)`
- `Part.makeCone(radius1, radius2, height, [point], [direction])` - Creates a cone with 2 radii
  *Example:* `cone = Part.makeCone(5, 0, 15)` # Pointed cone
- `Part.makeTorus(radius1, radius2, [point], [direction], [angle1], [angle2])` - Creates a torus
  *Example:* `torus = Part.makeTorus(10, 2)`
- `Part.makeWedge(xmin, ymin, zmin, z2min, x2min, xmax, ymax, zmax, z2max, x2max)` - Creates a wedge
  *Example:* `wedge = Part.makeWedge(0,0,0, 5,5, 10,10,10, 15,15)`
- `Part.makePrism(face, direction)` - Creates a prism from a face and direction vector
  *Example:* `face = Part.makePlane(10,10); prism = Part.makePrism(face, App.Vector(0,0,5))`
- `Part.makeHelix(pitch, height, radius, [angle])` - Creates a helix
  *Example:* `helix = Part.makeHelix(pitch=2, height=10, radius=3)`

*Boolean Operations:*
- `Part.cut(shape1, shape2)` - Cuts shape2 from shape1
  *Example:* `box1 = Part.makeBox(10,10,10); box2 = Part.makeBox(5,5,5, App.Vector(2,2,2)); cut_obj = Part.cut(box1, box2)`
- `Part.fuse(shape1, shape2)` - Combines shape1 and shape2
  *Example:* `cyl1 = Part.makeCylinder(5,10); cyl2 = Part.makeCylinder(3,15); fused_obj = Part.fuse(cyl1, cyl2)`
- `Part.common(shape1, shape2)` - Creates the intersection of shape1 and shape2
  *Example:* `sph1 = Part.makeSphere(10); sph2 = Part.makeSphere(8, App.Vector(5,0,0)); common_obj = Part.common(sph1, sph2)`
- `Part.section(shape1, shape2)` - Creates a cross-section between shape1 and shape2
  *Example:* `box = Part.makeBox(20,20,5); plane = Part.makePlane(30,30); section_obj = Part.section(box, plane)`

*Complex Shape Creation Operations:*
- `Part.makeLoft(listOfWires, [solid], [ruled])` - Creates a loft surface from a list of wires
  *Example:* `w1 = Part.makeWire([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(10,10,0), App.Vector(0,10,0)], True); w2 = Part.makeWire([App.Vector(2,2,5), App.Vector(8,2,5), App.Vector(8,8,5), App.Vector(2,8,5)], True); loft = Part.makeLoft([w1, w2], True)`
- `Part.makeSweep(spine, profile, [transition])` - Creates a sweep object along a path
  *Example:* `spine = Part.makeLine((0,0,0), (0,0,20)); profile = Part.makeCircle(5); sweep = Part.makeSweep(spine, profile)`
- `Part.makeExtrusion(face, vector)` - Extrudes a face along a vector
  *Example:* `face = Part.makePlane(10,10); extrusion = Part.makeExtrusion(face, App.Vector(0,0,15))`
- `Part.makeRevolution(profile, [placement], [angle], [solid])` - Creates a revolved object
  *Example:* `profile = Part.makeCircle(10, App.Vector(15,0,0)); revolution = Part.makeRevolution(profile)`
- `Part.makeShell(listOfFaces)` - Creates a shell from a list of faces (often for removing faces)
  *Example:* `box = Part.makeBox(10,10,10); faces_to_remove = [box.Faces[0]]; shell = Part.makeShell(faces_to_remove)`
- `Part.makeSolid(shell)` - Creates a solid from a shell
  *Example:* `# Assuming 'shell' is a valid Part.Shell object; solid = Part.makeSolid(shell)`
- `Part.makeCompound(listOfShapes)` - Creates a compound from a list of shapes
  *Example:* `box = Part.makeBox(5,5,5); sphere = Part.makeSphere(3); compound = Part.makeCompound([box, sphere])`
- `Part.makeFilledFace(listOfWires)` - Creates a face from a list of wires
  *Example:* `wire = Part.makeWire([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(5,5,0)], True); face = Part.makeFilledFace([wire])`
- `Part.makeOffset(shape, offset, [tolerance])` - Creates an offset object
  *Example:* `box = Part.makeBox(10,10,10); offset_box = Part.makeOffset(box, 2)`
- `Part.makeThickness(shape, thickness, [tolerance])` - Creates a thick object
  *Example:* `sphere = Part.makeSphere(10); thick_sphere = Part.makeThickness(sphere, -1)` # Negative for inward thickness

*Lines and Curves:*
- `Part.makeCircle(radius, [center], [direction], [angle1], [angle2])` - Creates a circle
  *Example:* `circle = Part.makeCircle(10)`
- `Part.makeEllipse(major_radius, minor_radius, [center], [direction])` - Creates an ellipse
  *Example:* `ellipse = Part.makeEllipse(10, 5)`
- `Part.makePolygon(listOfPoints)` - Creates a polygon from a list of points
  *Example:* `poly = Part.makePolygon([App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(5,10,0)])`
- `Part.makeSpline(listOfPoints, [periodic])` - Creates a spline
  *Example:* `spline = Part.makeSpline([App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)])`
- `Part.makeBSpline(poles, [knots], [mults], [periodic], [degree], [weights])` - Creates a BSpline
  *Example:* `poles = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bspline = Part.makeBSpline(poles)`
- `Part.makeBezierCurve(poles)` - Creates a Bezier curve
  *Example:* `poles = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bezier = Part.makeBezierCurve(poles)`

**2. PartDesign Workbench (Feature-based Modeling)**
*Sketch-based Features:*
- `PartDesign.makePad(sketch, length, [midplane], [reversed], [mode])` - Creates a pad feature (extrusion)
  *Example:* `# Assuming 'sketch' is a valid Sketcher.SketchObject; pad = PartDesign.makePad(sketch, 10)`
- `PartDesign.makePocket(sketch, length, [midplane], [reversed], [mode])` - Creates a pocket feature (cut extrusion)
  *Example:* `# Assuming 'sketch' is a valid Sketcher.SketchObject; pocket = PartDesign.makePocket(sketch, 5)`
- `PartDesign.makeRevolution(sketch, angle, [midplane], [reversed])` - Creates a revolution feature
  *Example:* `# Assuming 'sketch' is a valid Sketcher.SketchObject; revolution = PartDesign.makeRevolution(sketch, 360)`
- `PartDesign.makeGroove(sketch, angle, [midplane], [reversed])` - Creates a groove feature (revolved cut)
  *Example:* `# Assuming 'sketch' is a valid Sketcher.SketchObject; groove = PartDesign.makeGroove(sketch, 360)`

*Transitional Features:*
- `PartDesign.makeFillet(body, edges, radius)` - Creates an edge fillet
  *Example:* `# Assuming 'body' is a PartDesign.Body and 'edges' is a list of edges; fillet = PartDesign.makeFillet(body, edges, 2)`
- `PartDesign.makeChamfer(body, edges, distance)` - Creates an edge chamfer
  *Example:* `# Assuming 'body' is a PartDesign.Body and 'edges' is a list of edges; chamfer = PartDesign.makeChamfer(body, edges, 1)`
- `PartDesign.makeDraft(body, faces, angle)` - Creates a draft feature
  *Example:* `# Assuming 'body' is a PartDesign.Body and 'faces' is a list of faces; draft = PartDesign.makeDraft(body, faces, 5)` # 5 degree draft angle
- `PartDesign.makeThickness(body, faces, thickness, [mode])` - Creates a thickness feature
  *Example:* `# Assuming 'body' is a PartDesign.Body and 'faces' is a list of faces; thickness = PartDesign.makeThickness(body, faces, 1)`

*Pattern Features:*
- `PartDesign.makeLinearPattern(feature, direction, length, occurrences)` - Creates a linear pattern
  *Example:* `# Assuming 'feature' is a PartDesign feature; pattern = PartDesign.makeLinearPattern(feature, App.Vector(1,0,0), 50, 5)` # 5 occurrences over 50mm along X axis
- `PartDesign.makePolarPattern(feature, axis, angle, occurrences)` - Creates a polar pattern
  *Example:* `# Assuming 'feature' is a PartDesign feature and 'axis' is a reference axis; pattern = PartDesign.makePolarPattern(feature, axis, 360, 6)` # 6 occurrences over 360 degrees
- `PartDesign.makeMultiTransform(feature)` - Creates a multi-transform feature (combines multiple transformations)
  *Example:* `# Assuming 'feature' is a PartDesign feature; multi = PartDesign.makeMultiTransform(feature)` # Sub-transformations need to be defined
- `PartDesign.makeScaled(feature, factor, [center])` - Creates a scaled feature
  *Example:* `# Assuming 'feature' is a PartDesign feature; scaled = PartDesign.makeScaled(feature, 1.5)` # Scale factor 1.5
- `PartDesign.makeMirrored(feature, plane)` - Creates a mirrored feature
  *Example:* `# Assuming 'feature' is a PartDesign feature and 'plane' is a reference plane; mirrored = PartDesign.makeMirrored(feature, plane)`

*Advanced Features:*
- `PartDesign.makeLoft(profiles, [solid], [ruled])` - Creates a loft feature
  *Example:* `# Assuming 'profiles' is a list of Sketcher.SketchObject; loft = PartDesign.makeLoft(profiles, True)`
- `PartDesign.makePipe(spine, profile)` - Creates a pipe along a path
  *Example:* `# Assuming 'spine' and 'profile' are Sketcher.SketchObject; pipe = PartDesign.makePipe(spine, profile)`
- `PartDesign.makeAdditive(body, tool)` - Creates an additive feature (similar to Fuse)
  *Example:* `# Assuming 'body' and 'tool' are PartDesign features/bodies; additive = PartDesign.makeAdditive(body, tool)`
- `PartDesign.makeSubtractive(body, tool)` - Creates a subtractive feature (similar to Cut)
  *Example:* `# Assuming 'body' and 'tool' are PartDesign features/bodies; subtractive = PartDesign.makeSubtractive(body, tool)`

**3. Draft Workbench (Basic 2D and 3D Drawing)**
*Basic Objects (can be used in 3D space):*
- `Draft.makePoint(X, Y, Z)` - Creates a point
  *Example:* `pt = Draft.makePoint(10, 5, 2)`
- `Draft.makeLine(point1, point2)` - Creates a line
  *Example:* `p1 = App.Vector(0,0,0); p2 = App.Vector(10,10,10); line = Draft.makeLine(p1, p2)`
- `Draft.makeWire([points], [closed])` - Creates a wire from a list of points
  *Example:* `points = [App.Vector(0,0,0), App.Vector(10,0,0), App.Vector(10,10,0)]; wire = Draft.makeWire(points)`
- `Draft.makeBSpline([points], [closed])` - Creates a B-spline
  *Example:* `points = [App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bspline = Draft.makeBSpline(points)`
- `Draft.makeBezCurve([points], [closed])` - Creates a Bezier curve
  *Example:* `points = Simpson([App.Vector(0,0,0), App.Vector(5,5,0), App.Vector(10,0,0)]; bezcurve = Draft.makeBezCurve(points)`
- `Draft.makeCircle(radius, [placement])` - Creates a circle
  *Example:* `circle = Draft.makeCircle(5)`
- `Draft.makeEllipse(major_radius, minor_radius)` - Creates an ellipse
  *Example:* `ellipse = Draft.makeEllipse(10, 5)`
- `Draft.makeRectangle(length, width, [placement])` - Creates a rectangle
  *Example:* `rect = Draft.makeRectangle(20, 10)`
- `Draft.makePolygon(numberofedges, radius)` - Creates a regular polygon
  *Example:* `poly = Draft.makePolygon(6, 10)` # Hexagon with circumscribing circle radius 10
- `Draft.makeText(string, [placement])` - Creates text
  *Example:* `text = Draft.makeText("Hello FreeCAD")`
- `Draft.makeShapeString(string, fontfile, size)` - Creates a shape string (geometry from text)
  *Example:* `# Assuming font file exists; shape_string = Draft.makeShapeString("CAD", "C:/Windows/Fonts/arial.ttf", 10)`

*3D Operations:*
- `Draft.extrude(object, direction)` - Extrudes an object along a direction
  *Example:* `box = Part.makeBox(5,5,5); extruded_box = Draft.extrude(box, App.Vector(0,0,10))`
- `Draft.move(objectslist, vector, [copy])` - Moves objects in 3D space
  *Example:* `obj = Part.makeSphere(5); Draft.move(obj, App.Vector(10, 0, 0))`
- `Draft.rotate(objectslist, angle, center, axis, [copy])` - Rotates objects
  *Example:* `obj = Part.makeBox(10,10,10); Draft.rotate(obj, 45, App.Vector(0,0,0), App.Vector(0,0,1))` # Rotate 45 degrees around Z axis
- `Draft.scale(objectslist, scale, center, [copy])` - Scales objects
  *Example:* `obj = Part.makeCylinder(5,10); Draft.scale(obj, App.Vector(2,2,1))` # Scale X, Y by 2, Z remains the same
- `Draft.offset(object, offset, [copy])` - Creates an offset of an object
  *Example:* `line = Draft.makeLine(App.Vector(0,0,0), App.Vector(10,0,0)); offset_line = Draft.offset(line, 2)`

**4. Curve Workbench (Curve Extension)** (Note: May be an Addon)
- `Curve.makeBlendCurve([points], [degree])` - Creates a blend curve (smooth connection)
  *Example:* `# Assuming points list; blend = Curve.makeBlendCurve(points)`
- `Curve.makeCurveParametric(fx, fy, fz, bounds)` - Creates a parametric curve
  *Example:* `# Define parametric functions fx, fy, fz; curve = Curve.makeCurveParametric(fx, fy, fz, [0, 1])` # Parameter t from 0 to 1
- `Curve.makeBSplineApproximation(points, [degree])` - Creates a B-spline approximation from points
  *Example:* `# Assuming points list; approx = Curve.makeBSplineApproximation(points)`
- `Curve.makeCurveOnSurface(curve, surface)` - Creates a curve on a surface
  *Example:* `# Assuming curve and surface objects; curve_on_surf = Curve.makeCurveOnSurface(curve, surface)`
- `Curve.makePipeshell(spine, profiles)` - Creates a pipe shell along a spine and profiles
  *Example:* `# Assuming spine wire and profiles list; pipeshell = Curve.makePipeshell(spine, profiles)`
- `Curve.makeSweep(path, profile, [solid])` - Creates a sweep along a profile (similar to Part.makeSweep)
  *Example:* `# Assuming path wire and profile face; sweep = Curve.makeSweep(path, profile)`

**5. Surface Workbench (Working with Surfaces)** (Note: May be an Addon)
- `Surface.createBSplineSurface(poles, [uknots], [vknots], [umults], [vmults], [udegree], [vdegree])` - Creates a B-spline surface
  *Example:* `# Assuming poles grid, knots, mults, degrees; bsurf = Surface.createBSplineSurface(poles)`
- `Surface.createBezierSurface(poles)` - Creates a Bezier surface
  *Example:* `# Assuming poles grid; bezsurf = Surface.createBezierSurface(poles)`
- `Surface.createExtrusionSurface(profile, direction)` - Creates an extrusion surface
  *Example:* `# Assuming profile wire and direction vector; extrusurf = Surface.createExtrusionSurface(profile, direction)`
- `Surface.createRevolvedSurface(profile, center, axis, angle)` - Creates a revolved surface
  *Example:* `# Assuming profile wire, center, axis, angle; revsurf = Surface.createRevolvedSurface(profile, center, axis, 180)` # Revolve 180 degrees
- `Surface.createLoftSurface([profiles])` - Creates a loft surface
  *Example:* `# Assuming list of profile wires; loftsurf = Surface.createLoftSurface(profiles)`
- `Surface.createSweptSurface(profile, path)` - Creates a swept surface
  *Example:* `# Assuming profile wire and path wire; sweptsurf = Surface.createSweptSurface(profile, path)`
- `Surface.createBlendSurface(edge1, edge2)` - Creates a blend surface (smooth connection between 2 edges)
  *Example:* `# Assuming edge1 and edge2 Part.Edge objects; blendsurf = Surface.createBlendSurface(edge1, edge2)`
- `Surface.createFillingFace([edges])` - Creates a filling face from edges (creates a patch face)
  *Example:* `# Assuming list of edge objects forming a closed loop; fillsurf = Surface.createFillingFace(edges)`
- `Surface.createCurveNetworkSurface([edges])` - Creates a surface from a curve network
  *Example:* `# Assuming list of edge objects forming a network; netsurf = Surface.createCurveNetworkSurface(edges)`

**6. Mesh Workbench (Working with Meshes)**
- `Mesh.createBox(length, width, height)` - Creates a mesh box
  *Example:* `mesh_box = Mesh.createBox(10, 20, 5)`
- `Mesh.createSphere(radius, [sampling])` - Creates a mesh sphere
  *Example:* `mesh_sphere = Mesh.createSphere(10)`
- `Mesh.createCylinder(radius, height, [sampling])` - Creates a mesh cylinder
  *Example:* `mesh_cyl = Mesh.createCylinder(5, 30)`
- `Mesh.createCone(radius1, radius2, height, [sampling])` - Creates a mesh cone
  *Example:* `mesh_cone = Mesh.createCone(5, 0, 15)`
- `Mesh.createTorus(radius1, radius2, [sampling])` - Creates a mesh torus
  *Example:* `mesh_torus = Mesh.createTorus(10, 2)`
- `Mesh.createFromShape(shape, [linear_deflection], [angular_deflection])` - Creates a mesh from a shape (Part object)
  *Example:* `part_box = Part.makeBox(10,10,10); mesh_from_part = Mesh.createFromShape(part_box)`
- `Mesh.flipNormals(mesh)` - Flips the normal vectors of the mesh
  *Example:* `# Assuming 'mesh' is a Mesh object; Mesh.flipNormals(mesh)`
- `Mesh.harmonizeNormals(mesh)` - Harmonizes normal vectors (outward or inward direction)
  *Example:* `# Assuming 'mesh' is a Mesh object; Mesh.harmonizeNormals(mesh)`
- `Mesh.smooth(mesh, [iterations])` - Smooths the mesh
  *Example:* `# Assuming 'mesh' is a Mesh object; smoothed_mesh = Mesh.smooth(mesh)`
- `Mesh.refine(mesh)` - Refines the mesh (adds detail)
  *Example:* `# Assuming 'mesh' is a Mesh object; refined_mesh = Mesh.refine(mesh)`

**7. Points Workbench (Working with Points)** (Note: May be an Addon)
- `Points.createPoints([points])` - Creates a point cloud
  *Example:* `points_list = [(0,0,0), (1,1,1), (2,0,2)]; cloud = Points.createPoints(points_list)`
- `Points.convertToBSpline(points)` - Converts points to a B-spline
  *Example:* `# Assuming 'points' is a Points object; bspline = Points.convertToBSpline(points)`
- `Points.show(points)` - Displays the point cloud
  *Example:* `# Assuming 'points' is a Points object; Points.show(points)`
- `Points.export(pointcloud, filename)` - Exports the point cloud
  *Example:* `# Assuming 'pointcloud' is a Points object; Points.export(pointcloud, "cloud.asc")`

**8. Robot Workbench (Robot Simulation)**
- `Robot.createRobot(name)` - Creates a new robot
  *Example:* `robot = Robot.createRobot("MyRobot")`
- `Robot.createTrajectory(name)` - Creates a trajectory
  *Example:* `traj = Robot.createTrajectory("Path1")`
- `Robot.createEdge(p1, p2)` - Creates an edge for the trajectory (usually internal use)
  *Example:* `# Assuming p1, p2 are points/vectors; edge = Robot.createEdge(p1, p2)`
- `Robot.createRobotTrajectory(robot, trajectory)` - Creates a trajectory for the robot
  *Example:* `# Assuming 'robot' and 'trajectory' objects; Robot.createRobotTrajectory(robot, trajectory)`

**9. Assembly Workbench (Assembling Components)** (Note: Syntax depends on the specific Assembly WB, e.g., A2plus, Assembly3, Assembly4)
- `Assembly.createAssembly()` - Creates a new assembly (general)
- `Assembly.addPart(assembly, part, [transform])` - Adds a part to the assembly (general)
- `Assembly.addConstraint(assembly, constraint)` - Adds a constraint to the assembly (general)
  *Example (A2plus):*
    ```python
    # import a2plus.importpart as importer
    # assembly_doc = App.newDocument("Assembly")
    # part1_proxy = importer.importPartFromFile(assembly_doc, "part1.fcstd")
    # part2_proxy = importer.importPartFromFile(assembly_doc, "part2.fcstd")
    # # Add constraints using A2plus GUI or specific constraint functions
    ```

**10. Arch Workbench (Architectural 3D Modeling)**
- `Arch.makeWall(baseobj, [length], [width], [height])` - Creates a wall
  *Example:* `line = Draft.makeLine(App.Vector(0,0,0), App.Vector(5000,0,0)); wall = Arch.makeWall(line, height=3000, width=200)`
- `Arch.makeStructure(baseobj, [length], [width], [height])` - Creates a structure (beam, column)
  *Example:* `rect = Draft.makeRectangle(100, 100); struct = Arch.makeStructure(rect, height=3000)` # Column
- `Arch.makeRoof(baseobj, [slopes])` - Creates a roof
  *Example:* `outline = Draft.makeRectangle(10000, 5000); roof = Arch.makeRoof(outline)`
- `Arch.makeFloor([objects])` - Creates a floor
  *Example:* `# Assuming 'walls' is a list of Arch Wall objects; floor = Arch.makeFloor(walls)`
- `Arch.makeBuilding([objects])` - Creates a building (groups floors)
  *Example:* `# Assuming 'floors' is a list of Arch Floor objects; building = Arch.makeBuilding(floors)`
- `Arch.makeSite([objects])` - Creates a building site (groups buildings)
  *Example:* `# Assuming 'buildings' is a list of Arch Building objects; site = Arch.makeSite(buildings)`
- `Arch.makeWindow(baseobj, [width], [height])` - Creates a window (usually placed on a wall)
  *Example:* `# Assuming 'wall_face' is a face on an Arch Wall; window = Arch.makeWindow(wall_face, width=1200, height=1000)`
- `Arch.makeDoor(baseobj, [width], [height])` - Creates a door
  *Example:* `# Assuming 'wall_face' is a face on an Arch Wall; door = Arch.makeDoor(wall_face, width=900, height=2100)`
- `Arch.makePipe(baseobj, [diameter])` - Creates a pipe
  *Example:* `wire = Draft.makeWire([App.Vector(0,0,0), App.Vector(0,5000,0)]); pipe = Arch.makePipe(wire, diameter=100)`
- `Arch.makeStairs(baseobj, [length], [width], [height])` - Creates stairs
  *Example:* `baseline = Draft.makeLine(App.Vector(0,0,0), App.Vector(3000,0,0)); stairs = Arch.makeStairs(baseline, width=1000, height=2800)`
- `Arch.makeRebar(baseobj, [diameter], [spacing])` - Creates rebar (in structures)
  *Example:* `# Assuming 'structure' is an Arch Structure object; rebar = Arch.makeRebar(structure, diameter=12, spacing=150)`